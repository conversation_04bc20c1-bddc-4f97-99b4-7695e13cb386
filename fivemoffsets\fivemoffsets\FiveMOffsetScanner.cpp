#include "pch.h"
#include "FiveMOffsetScanner.h"

// Global instance
std::unique_ptr<FiveMOffsetScanner> g_OffsetScanner = nullptr;

FiveMOffsetScanner::FiveMOffsetScanner() : m_is_initialized(false) {
    m_pattern_scanner = std::make_unique<PatternScanner>();
    m_structures = std::make_unique<FiveMStructures>();
    m_hook_manager = std::make_unique<HookManager>();
    m_file_manager = std::make_unique<FileManager>();
}

FiveMOffsetScanner::~FiveMOffsetScanner() {
    Shutdown();
}

bool FiveMOffsetScanner::Initialize(const ScanConfig& config) {
    if (m_is_initialized) {
        return true;
    }
    
    m_config = config;
    
    SCAN_LOG_INFO("Initializing FiveM Offset Scanner...");
    
    // Initialize logging first
    InitializeLogging();
    
    // Initialize file manager
    if (!m_file_manager->Initialize()) {
        SCAN_LOG_ERROR("Failed to initialize file manager");
        return false;
    }
    
    // Initialize pattern scanner
    if (m_config.enable_pattern_scanning) {
        if (!m_pattern_scanner->Initialize()) {
            SCAN_LOG_ERROR("Failed to initialize pattern scanner");
            return false;
        }
        SCAN_LOG_INFO("Pattern scanner initialized successfully");
    }
    
    // Initialize structure detector
    if (m_config.enable_structure_detection) {
        if (!m_structures->Initialize()) {
            SCAN_LOG_ERROR("Failed to initialize structure detector");
            return false;
        }
        SCAN_LOG_INFO("Structure detector initialized successfully");
    }
    
    // Initialize hook manager (paid feature)
    if (m_config.enable_hooking && m_config.paid_features_enabled) {
        if (!m_hook_manager->Initialize()) {
            SCAN_LOG_ERROR("Failed to initialize hook manager");
            return false;
        }
        SCAN_LOG_INFO("Hook manager initialized successfully");
    }
    
    m_is_initialized = true;
    SCAN_LOG_INFO("FiveM Offset Scanner initialized successfully");
    
    return true;
}

void FiveMOffsetScanner::Shutdown() {
    if (!m_is_initialized) {
        return;
    }
    
    SCAN_LOG_INFO("Shutting down FiveM Offset Scanner...");
    
    if (m_hook_manager) {
        m_hook_manager->Shutdown();
    }
    
    m_is_initialized = false;
    SCAN_LOG_INFO("FiveM Offset Scanner shutdown complete");
}

FiveMOffsetScanner::ScanResults FiveMOffsetScanner::PerformFullScan() {
    ScanResults results;
    
    if (!m_is_initialized) {
        results.error_message = "Scanner not initialized";
        return results;
    }
    
    SCAN_LOG_INFO("Starting full scan...");
    auto start_time = std::chrono::high_resolution_clock::now();
    
    bool scan_success = true;
    
    // Scan patterns
    if (m_config.enable_pattern_scanning) {
        if (!PerformScanWithTiming("Pattern Scanning", [this]() { return ScanPatterns(); })) {
            scan_success = false;
            results.error_message += "Pattern scanning failed; ";
        }
    }
    
    // Scan structures
    if (m_config.enable_structure_detection) {
        if (!PerformScanWithTiming("Structure Detection", [this]() { return ScanStructures(); })) {
            scan_success = false;
            results.error_message += "Structure detection failed; ";
        }
    }
    
    // Setup hooks (paid feature)
    if (m_config.enable_hooking && m_config.paid_features_enabled) {
        if (!PerformScanWithTiming("Hook Setup", [this]() { return SetupHooks(); })) {
            scan_success = false;
            results.error_message += "Hook setup failed; ";
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    results.scan_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Collect results
    if (scan_success) {
        auto offsets = m_structures->GetAllOffsets();
        results.offsets_found = offsets.size();
        results.patterns_matched = m_pattern_scanner->GetAllResults().size();
        
        if (m_config.enable_hooking && m_config.paid_features_enabled) {
            auto hooks = m_hook_manager->GetAllHooks();
            results.hooks_created = hooks.size();
        }
        
        results.success = true;
        SCAN_LOG_INFO("Full scan completed successfully");
        
        // Auto-export if enabled
        if (m_config.enable_auto_export) {
            ExportResults();
        }
    } else {
        SCAN_LOG_ERROR("Full scan failed: " + results.error_message);
    }
    
    m_last_results = results;
    return results;
}

bool FiveMOffsetScanner::ScanPatterns() {
    if (!m_pattern_scanner->IsInitialized()) {
        return false;
    }
    
    SCAN_LOG_DEBUG("Scanning patterns...");
    return m_pattern_scanner->ScanAllPatterns();
}

bool FiveMOffsetScanner::ScanStructures() {
    if (!m_structures) {
        return false;
    }
    
    SCAN_LOG_DEBUG("Scanning structures...");
    return m_structures->ScanAllStructures();
}

bool FiveMOffsetScanner::SetupHooks() {
    if (!m_hook_manager->IsInitialized() || !m_config.paid_features_enabled) {
        return false;
    }
    
    SCAN_LOG_DEBUG("Setting up hooks...");
    // Enable all available hooks
    m_hook_manager->EnableAllHooks();
    return true;
}

nlohmann::json FiveMOffsetScanner::GetOffsetsJson() const {
    if (!m_structures) {
        return nlohmann::json{};
    }
    
    return m_structures->ExportToJson();
}

nlohmann::json FiveMOffsetScanner::GetHooksJson() const {
    if (!m_hook_manager || !m_config.paid_features_enabled) {
        return nlohmann::json{};
    }
    
    return m_hook_manager->ExportToJson();
}

nlohmann::json FiveMOffsetScanner::GetCombinedJson() const {
    nlohmann::json combined;
    combined["metadata"] = m_file_manager->CreateMetadata();
    combined["scan_results"] = {
        {"success", m_last_results.success},
        {"offsets_found", m_last_results.offsets_found},
        {"hooks_created", m_last_results.hooks_created},
        {"patterns_matched", m_last_results.patterns_matched},
        {"scan_duration_ms", m_last_results.scan_duration.count()}
    };
    combined["offsets"] = GetOffsetsJson();
    
    if (m_config.paid_features_enabled) {
        combined["hooks"] = GetHooksJson();
    }
    
    return combined;
}

bool FiveMOffsetScanner::ExportResults() {
    if (!m_file_manager->IsInitialized()) {
        return false;
    }
    
    SCAN_LOG_INFO("Exporting scan results...");
    
    bool success = true;
    
    // Export offsets
    if (m_config.enable_structure_detection) {
        nlohmann::json offsets_data = GetOffsetsJson();
        if (!m_file_manager->ExportOffsets(offsets_data)) {
            SCAN_LOG_ERROR("Failed to export offsets");
            success = false;
        }
    }
    
    // Export hooks (paid feature)
    if (m_config.enable_hooking && m_config.paid_features_enabled) {
        nlohmann::json hooks_data = GetHooksJson();
        if (!m_file_manager->ExportHooks(hooks_data)) {
            SCAN_LOG_ERROR("Failed to export hooks");
            success = false;
        }
    }
    
    // Export combined data
    nlohmann::json combined_data = GetCombinedJson();
    if (!m_file_manager->ExportCombined(GetOffsetsJson(), GetHooksJson())) {
        SCAN_LOG_ERROR("Failed to export combined data");
        success = false;
    }
    
    if (success) {
        SCAN_LOG_INFO("Results exported successfully to: " + m_file_manager->GetOffsetsDirectory());
    }
    
    return success;
}

void FiveMOffsetScanner::InitializeLogging() {
    if (!m_config.enable_logging) {
        return;
    }
    
    // Create log file in results directory
    std::string timestamp = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count());
    m_log_file = "fivem_scanner_" + timestamp + ".log";
}

void FiveMOffsetScanner::LogMessage(const std::string& level, const std::string& message) {
    if (!m_config.enable_logging) {
        return;
    }
    
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::stringstream ss;
    ss << "[" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "] ";
    ss << "[" << level << "] " << message << std::endl;
    
    // Write to file if file manager is available
    if (m_file_manager && m_file_manager->IsInitialized()) {
        std::string log_content = ss.str();
        std::string log_path = FileUtils::JoinPath(m_file_manager->GetResultsDirectory(), m_log_file);
        
        std::ofstream log_file(log_path, std::ios::app);
        if (log_file.is_open()) {
            log_file << log_content;
            log_file.close();
        }
    }
    
    // Also output to console for debugging
    #ifdef _DEBUG
    OutputDebugStringA(ss.str().c_str());
    #endif
}

void FiveMOffsetScanner::LogError(const std::string& message) {
    LogMessage("ERROR", message);
}

void FiveMOffsetScanner::LogInfo(const std::string& message) {
    LogMessage("INFO", message);
}

void FiveMOffsetScanner::LogDebug(const std::string& message) {
    LogMessage("DEBUG", message);
}

bool FiveMOffsetScanner::PerformScanWithTiming(const std::string& scan_name, std::function<bool()> scan_function) {
    SCAN_LOG_DEBUG("Starting " + scan_name + "...");
    auto start = std::chrono::high_resolution_clock::now();
    
    bool result = scan_function();
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    if (result) {
        SCAN_LOG_DEBUG(scan_name + " completed in " + std::to_string(duration.count()) + "ms");
    } else {
        SCAN_LOG_ERROR(scan_name + " failed after " + std::to_string(duration.count()) + "ms");
    }
    
    return result;
}

// DLL Export Functions
extern "C" {
    __declspec(dllexport) bool InitializeScanner() {
        try {
            if (!g_OffsetScanner) {
                g_OffsetScanner = std::make_unique<FiveMOffsetScanner>();
            }

            FiveMOffsetScanner::ScanConfig config;
            config.enable_pattern_scanning = true;
            config.enable_structure_detection = true;
            config.enable_hooking = false; // Disabled by default
            config.enable_auto_export = true;
            config.enable_logging = true;
            config.paid_features_enabled = false;

            return g_OffsetScanner->Initialize(config);
        }
        catch (const std::exception&) {
            return false;
        }
    }

    __declspec(dllexport) bool PerformScan() {
        try {
            if (!g_OffsetScanner || !g_OffsetScanner->IsInitialized()) {
                return false;
            }

            auto results = g_OffsetScanner->PerformFullScan();
            return results.success;
        }
        catch (const std::exception&) {
            return false;
        }
    }

    __declspec(dllexport) bool ExportResults() {
        try {
            if (!g_OffsetScanner || !g_OffsetScanner->IsInitialized()) {
                return false;
            }

            return g_OffsetScanner->ExportResults();
        }
        catch (const std::exception&) {
            return false;
        }
    }

    __declspec(dllexport) const char* GetResultsJson() {
        try {
            if (!g_OffsetScanner || !g_OffsetScanner->IsInitialized()) {
                return nullptr;
            }

            static std::string json_string;
            nlohmann::json results = g_OffsetScanner->GetCombinedJson();
            json_string = results.dump(4);
            return json_string.c_str();
        }
        catch (const std::exception&) {
            return nullptr;
        }
    }

    __declspec(dllexport) const char* GetStatusString() {
        try {
            if (!g_OffsetScanner) {
                return "Scanner not initialized";
            }

            static std::string status_string;
            status_string = g_OffsetScanner->GetStatusString();
            return status_string.c_str();
        }
        catch (const std::exception&) {
            return "Error getting status";
        }
    }

    __declspec(dllexport) void ShutdownScanner() {
        try {
            if (g_OffsetScanner) {
                g_OffsetScanner->Shutdown();
                g_OffsetScanner.reset();
            }
        }
        catch (const std::exception&) {
            // Ignore exceptions during shutdown
        }
    }

    __declspec(dllexport) bool EnableHooking(const char* license_key) {
        try {
            if (!g_OffsetScanner || !license_key) {
                return false;
            }

            return g_OffsetScanner->EnablePaidFeatures(std::string(license_key));
        }
        catch (const std::exception&) {
            return false;
        }
    }

    __declspec(dllexport) bool SetupHook(const char* hook_name) {
        try {
            if (!g_OffsetScanner || !hook_name) {
                return false;
            }

            // Implementation would depend on hook manager
            return false; // Placeholder
        }
        catch (const std::exception&) {
            return false;
        }
    }

    __declspec(dllexport) bool RemoveHook(const char* hook_name) {
        try {
            if (!g_OffsetScanner || !hook_name) {
                return false;
            }

            // Implementation would depend on hook manager
            return false; // Placeholder
        }
        catch (const std::exception&) {
            return false;
        }
    }
}

// Additional implementation methods
std::string FiveMOffsetScanner::GetStatusString() const {
    if (!m_is_initialized) {
        return "Not initialized";
    }

    std::stringstream ss;
    ss << "FiveM Offset Scanner Status:\n";
    ss << "- Initialized: " << (m_is_initialized ? "Yes" : "No") << "\n";
    ss << "- Pattern Scanning: " << (m_config.enable_pattern_scanning ? "Enabled" : "Disabled") << "\n";
    ss << "- Structure Detection: " << (m_config.enable_structure_detection ? "Enabled" : "Disabled") << "\n";
    ss << "- Hooking: " << (m_config.enable_hooking && m_config.paid_features_enabled ? "Enabled" : "Disabled") << "\n";
    ss << "- Last Scan Results:\n";
    ss << "  - Success: " << (m_last_results.success ? "Yes" : "No") << "\n";
    ss << "  - Offsets Found: " << m_last_results.offsets_found << "\n";
    ss << "  - Patterns Matched: " << m_last_results.patterns_matched << "\n";
    ss << "  - Hooks Created: " << m_last_results.hooks_created << "\n";
    ss << "  - Scan Duration: " << m_last_results.scan_duration.count() << "ms\n";

    return ss.str();
}

bool FiveMOffsetScanner::EnablePaidFeatures(const std::string& license_key) {
    if (ValidateLicense(license_key)) {
        m_config.paid_features_enabled = true;
        m_config.enable_hooking = true;
        SCAN_LOG_INFO("Paid features enabled successfully");
        return true;
    }

    SCAN_LOG_ERROR("Invalid license key provided");
    return false;
}

bool FiveMOffsetScanner::ValidateLicense(const std::string& license_key) {
    // Simple license validation (in real implementation, this would be more secure)
    return license_key == "FIVEM_OFFSET_SCANNER_PRO_2024" ||
           license_key == "DEMO_LICENSE_KEY";
}
