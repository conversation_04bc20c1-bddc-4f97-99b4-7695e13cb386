// dllmain.cpp : Define o ponto de entrada para o aplicativo DLL.
#include "pch.h"
#include "FiveMOffsetScanner.h"

// Global variables
HMODULE g_hModule = nullptr;
std::thread g_ScanThread;
bool g_AutoScanEnabled = true;

// Forward declarations
void PerformAutoScan();
void CreateConsoleWindow();
void PrintWelcomeMessage();

BOOL APIENTRY DllMain(HMODULE hModule,
                     DWORD  ul_reason_for_call,
                     LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
    {
        g_hModule = hModule;

        // Disable DLL_THREAD_ATTACH and DLL_THREAD_DETACH notifications for performance
        DisableThreadLibraryCalls(hModule);

        // SEMPRE criar console para feedback visual
        CreateConsoleWindow();

        PrintWelcomeMessage();

        // SEMPRE executar o scan automaticamente quando a DLL é injetada
        // Não importa se é FiveM ou não, vamos tentar
        g_ScanThread = std::thread(PerformAutoScan);
        g_ScanThread.detach();

        break;
    }
    case DLL_PROCESS_DETACH:
    {
        // Cleanup
        if (g_OffsetScanner) {
            g_OffsetScanner->Shutdown();
            g_OffsetScanner.reset();
        }

        // Free console if created
        #ifdef _DEBUG
        FreeConsole();
        #endif

        break;
    }
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
        break;
    }
    return TRUE;
}

// Function to detect if we're running in FiveM
bool IsFiveMProcess() {
    char processName[MAX_PATH];
    GetModuleFileNameA(nullptr, processName, MAX_PATH);

    std::string procName = processName;
    std::transform(procName.begin(), procName.end(), procName.begin(), ::tolower);

    return (procName.find("fivem") != std::string::npos ||
            procName.find("citizenfx") != std::string::npos ||
            procName.find("gta5") != std::string::npos);
}

// Function to wait for process to be ready (mais flexível)
bool WaitForProcessReady() {
    OutputDebugStringA("[FiveMOffsets] Verificando se o processo está pronto...\n");

    // Verificar se temos acesso básico ao processo
    HANDLE hProcess = GetCurrentProcess();
    if (hProcess == INVALID_HANDLE_VALUE) {
        OutputDebugStringA("[FiveMOffsets] Não foi possível acessar o processo atual\n");
        return false;
    }

    // Verificar se conseguimos ler memória básica
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery((LPCVOID)0x10000, &mbi, sizeof(mbi)) == 0) {
        OutputDebugStringA("[FiveMOffsets] Não foi possível consultar memória virtual\n");
        return false;
    }

    OutputDebugStringA("[FiveMOffsets] ✅ Processo pronto para scan!\n");
    return true;
}

void PerformAutoScan() {
    try {
        OutputDebugStringA("[FiveMOffsets] ========================================\n");
        OutputDebugStringA("[FiveMOffsets] DLL INJETADA! Iniciando scan automático...\n");
        OutputDebugStringA("[FiveMOffsets] ========================================\n");

        // Mostrar console imediatamente
        std::cout << "\n🎯 FIVEM OFFSET SCANNER ATIVADO!\n";
        std::cout << "📍 DLL injetada com sucesso!\n";
        std::cout << "🔄 Iniciando scan automático...\n\n";

        // Check if we're in a FiveM process (mas não abortar se não for)
        bool isFiveM = IsFiveMProcess();
        if (isFiveM) {
            OutputDebugStringA("[FiveMOffsets] ✅ Processo FiveM detectado!\n");
            std::cout << "✅ Processo FiveM detectado!\n";
        } else {
            OutputDebugStringA("[FiveMOffsets] ⚠️ Processo FiveM não detectado, mas continuando...\n");
            std::cout << "⚠️ Processo FiveM não detectado, mas tentando scan...\n";
        }

        // Aguardar um pouco para estabilização (menos tempo)
        OutputDebugStringA("[FiveMOffsets] ⏳ Aguardando estabilização (3 segundos)...\n");
        std::cout << "⏳ Aguardando estabilização...\n";
        std::this_thread::sleep_for(std::chrono::seconds(3));

        // Initialize scanner
        OutputDebugStringA("[FiveMOffsets] 🔧 Inicializando scanner...\n");
        std::cout << "🔧 Inicializando scanner...\n";

        if (!InitializeScanner()) {
            OutputDebugStringA("[FiveMOffsets] ❌ Falha ao inicializar scanner\n");
            std::cout << "❌ Falha ao inicializar scanner!\n";
            MessageBoxA(nullptr,
                       "❌ ERRO: Falha ao inicializar o scanner!\n\n"
                       "Possíveis causas:\n"
                       "• Processo não compatível\n"
                       "• Falta de permissões\n"
                       "• Antivírus bloqueando",
                       "FiveM Scanner - Erro de Inicialização",
                       MB_OK | MB_ICONERROR | MB_TOPMOST);
            return;
        }

        OutputDebugStringA("[FiveMOffsets] ✅ Scanner inicializado com sucesso!\n");
        std::cout << "✅ Scanner inicializado com sucesso!\n";

        // Perform scan
        OutputDebugStringA("[FiveMOffsets] 🔍 Iniciando scan de padrões...\n");
        std::cout << "🔍 Iniciando scan de padrões...\n";
        std::cout.flush(); // Forçar output imediato

        // Adicionar timeout para evitar travamento
        std::cout << "⏱️ Executando scan com timeout de 30 segundos...\n";
        std::cout.flush();

        bool scanResult = false;
        std::atomic<bool> scanCompleted(false);
        std::atomic<bool> scanSuccess(false);

        // Executar scan em thread separada com timeout
        std::thread scanThread([&]() {
            try {
                OutputDebugStringA("[FiveMOffsets] Thread de scan iniciada...\n");
                std::cout << "🔄 Thread de scan iniciada...\n";
                std::cout.flush();

                scanSuccess = PerformScan();
                scanCompleted = true;

                OutputDebugStringA("[FiveMOffsets] Thread de scan finalizada\n");
                std::cout << "✅ Thread de scan finalizada\n";
                std::cout.flush();
            }
            catch (const std::exception& e) {
                OutputDebugStringA(("[FiveMOffsets] Exceção na thread de scan: " + std::string(e.what()) + "\n").c_str());
                std::cout << "❌ Exceção na thread de scan: " << e.what() << "\n";
                scanCompleted = true;
                scanSuccess = false;
            }
            catch (...) {
                OutputDebugStringA("[FiveMOffsets] Exceção desconhecida na thread de scan\n");
                std::cout << "❌ Exceção desconhecida na thread de scan\n";
                scanCompleted = true;
                scanSuccess = false;
            }
        });

        // Aguardar com timeout
        auto startTime = std::chrono::steady_clock::now();
        const auto timeoutDuration = std::chrono::seconds(30);

        while (!scanCompleted) {
            std::this_thread::sleep_for(std::chrono::milliseconds(500));

            auto elapsed = std::chrono::steady_clock::now() - startTime;
            if (elapsed > timeoutDuration) {
                OutputDebugStringA("[FiveMOffsets] ⏰ Timeout do scan! Forçando finalização...\n");
                std::cout << "⏰ Timeout do scan! Forçando finalização...\n";
                std::cout.flush();

                // Tentar finalizar a thread graciosamente
                scanThread.detach(); // Deixar a thread rodar em background

                MessageBoxA(nullptr,
                           "⏰ TIMEOUT DO SCAN!\n\n"
                           "O scan demorou mais de 30 segundos.\n"
                           "Isso pode indicar:\n"
                           "• Processo muito grande para scan\n"
                           "• Proteção de memória ativa\n"
                           "• Processo não compatível\n\n"
                           "Tente injetar em um processo menor ou diferente.",
                           "FiveM Scanner - Timeout",
                           MB_OK | MB_ICONWARNING | MB_TOPMOST);
                return;
            }

            // Mostrar progresso a cada 5 segundos
            if (std::chrono::duration_cast<std::chrono::seconds>(elapsed).count() % 5 == 0) {
                int secondsElapsed = std::chrono::duration_cast<std::chrono::seconds>(elapsed).count();
                std::cout << "⏳ Scan em progresso... " << secondsElapsed << "s\n";
                std::cout.flush();
            }
        }

        // Aguardar thread finalizar
        if (scanThread.joinable()) {
            scanThread.join();
        }

        if (scanSuccess) {
            OutputDebugStringA("[FiveMOffsets] ✅ Scan concluído com sucesso!\n");
            std::cout << "✅ Scan concluído com sucesso!\n";

            // Export results
            OutputDebugStringA("[FiveMOffsets] 📁 Exportando resultados...\n");
            std::cout << "📁 Exportando resultados...\n";

            if (ExportResults()) {
                OutputDebugStringA("[FiveMOffsets] ✅ Resultados exportados para results/offsets/\n");
                std::cout << "✅ Resultados exportados para results/offsets/\n";
                std::cout << "\n🎉 SCAN COMPLETO! Verifique a pasta results/offsets/\n";

                // Show success notification
                MessageBoxA(nullptr,
                           "🎉 FiveM Offset Scanner - SUCESSO!\n\n"
                           "✅ Scan concluído com sucesso!\n"
                           "📁 Resultados salvos em: results/offsets/\n"
                           "📄 Arquivos gerados:\n"
                           "   • offsets.json (dados JSON)\n"
                           "   • offsets.ct (Cheat Engine)\n"
                           "   • offsets.h (headers C++)\n\n"
                           "🔥 Scanner funcionando perfeitamente!",
                           "FiveM Scanner - SUCESSO",
                           MB_OK | MB_ICONINFORMATION | MB_TOPMOST);
            } else {
                OutputDebugStringA("[FiveMOffsets] ❌ Falha ao exportar resultados\n");
                std::cout << "❌ Falha ao exportar resultados!\n";
                MessageBoxA(nullptr,
                           "⚠️ Scanner concluído mas falha na exportação!\n\n"
                           "Verifique se a pasta results/offsets/ tem permissão de escrita.\n"
                           "Tente executar como administrador.",
                           "FiveM Scanner - Erro de Exportação",
                           MB_OK | MB_ICONWARNING | MB_TOPMOST);
            }
        } else {
            OutputDebugStringA("[FiveMOffsets] ❌ Scan falhou\n");
            std::cout << "❌ Scan falhou!\n";
            MessageBoxA(nullptr,
                       "❌ FiveM Offset Scanner - FALHA NO SCAN\n\n"
                       "Possíveis causas:\n"
                       "• FiveM não está totalmente carregado\n"
                       "• Processo incorreto (precisa ser GTA5.exe)\n"
                       "• Proteção de memória ativa\n"
                       "• Versão incompatível do FiveM\n\n"
                       "💡 Dica: Tente injetar quando estiver no menu principal do FiveM",
                       "FiveM Scanner - Erro de Scan",
                       MB_OK | MB_ICONERROR | MB_TOPMOST);
        }
    }
    catch (const std::exception& e) {
        std::string error_msg = "[FiveMOffsets] Exception during auto scan: ";
        error_msg += e.what();
        error_msg += "\n";
        OutputDebugStringA(error_msg.c_str());

        MessageBoxA(nullptr,
                   ("FiveM Offset Scanner encountered an error:\n\n" + std::string(e.what())).c_str(),
                   "FiveM Scanner - Exception",
                   MB_OK | MB_ICONERROR | MB_TOPMOST);
    }
    catch (...) {
        OutputDebugStringA("[FiveMOffsets] Unknown exception during auto scan\n");
        MessageBoxA(nullptr,
                   "FiveM Offset Scanner encountered an unknown error.",
                   "FiveM Scanner - Unknown Error",
                   MB_OK | MB_ICONERROR | MB_TOPMOST);
    }
}

void CreateConsoleWindow() {
    // Sempre tentar criar console para feedback visual
    if (AllocConsole()) {
        FILE* pCout;
        FILE* pCin;
        FILE* pCerr;

        freopen_s(&pCout, "CONOUT$", "w", stdout);
        freopen_s(&pCin, "CONIN$", "r", stdin);
        freopen_s(&pCerr, "CONOUT$", "w", stderr);

        SetConsoleTitleA("🎯 FiveM Offset Scanner - ATIVO");

        // Make cout, wcout, cin, wcin, wcerr, cerr, wclog and clog
        // point to console as well
        std::ios::sync_with_stdio(true);
        std::wcout.clear();
        std::cout.clear();
        std::wcerr.clear();
        std::cerr.clear();
        std::wcin.clear();
        std::cin.clear();

        // Configurar console para melhor visibilidade
        HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
        if (hConsole != INVALID_HANDLE_VALUE) {
            // Definir cor verde para destacar
            SetConsoleTextAttribute(hConsole, FOREGROUND_GREEN | FOREGROUND_INTENSITY);
        }

        // Mostrar que o console foi criado
        std::cout << "🎯 CONSOLE ATIVO - FiveM Offset Scanner\n";
        std::cout << "========================================\n";
    } else {
        // Se não conseguir criar console, usar OutputDebugString
        OutputDebugStringA("[FiveMOffsets] Não foi possível criar console, usando DebugView\n");
    }
}

void PrintWelcomeMessage() {
    const char* welcome_msg =
        "[FiveMOffsets] ========================================\n"
        "[FiveMOffsets] 🎯 FiveM Offset Scanner v1.0 - INJETADO!\n"
        "[FiveMOffsets] 🚀 Scan Automático Ativado!\n"
        "[FiveMOffsets] ========================================\n"
        "[FiveMOffsets] Features:\n"
        "[FiveMOffsets] ✅ Scan automático ao injetar\n"
        "[FiveMOffsets] ✅ Detecção de padrões avançada\n"
        "[FiveMOffsets] ✅ Export automático (JSON, CT, H)\n"
        "[FiveMOffsets] ✅ Funciona sem estar em servidor\n"
        "[FiveMOffsets] ✅ Console visual para feedback\n"
        "[FiveMOffsets] ========================================\n"
        "[FiveMOffsets] 🔥 DLL PRONTA! Iniciando scan...\n";

    OutputDebugStringA(welcome_msg);

    // SEMPRE mostrar no console (não apenas em debug)
    std::cout << "\n🎯 FIVEM OFFSET SCANNER v1.0\n";
    std::cout << "========================================\n";
    std::cout << "🚀 DLL INJETADA COM SUCESSO!\n";
    std::cout << "🔥 Scan automático será iniciado...\n";
    std::cout << "========================================\n\n";
}

