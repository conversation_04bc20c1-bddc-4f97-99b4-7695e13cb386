// dllmain.cpp : Define o ponto de entrada para o aplicativo DLL.
#include "pch.h"
#include "FiveMOffsetScanner.h"

// Global variables
HMODULE g_hModule = nullptr;
std::thread g_ScanThread;
bool g_AutoScanEnabled = true;

// Forward declarations
void PerformAutoScan();
void CreateConsoleWindow();
void PrintWelcomeMessage();

BOOL APIENTRY DllMain(HMODULE hModule,
                     DWORD  ul_reason_for_call,
                     LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
    {
        g_hModule = hModule;

        // Disable DLL_THREAD_ATTACH and DLL_THREAD_DETACH notifications for performance
        DisableThreadLibraryCalls(hModule);

        // Create console for debugging (optional)
        #ifdef _DEBUG
        CreateConsoleWindow();
        #endif

        PrintWelcomeMessage();

        // Initialize the scanner in a separate thread to avoid blocking DLL load
        if (g_AutoScanEnabled) {
            g_ScanThread = std::thread(PerformAutoScan);
            g_ScanThread.detach();
        }

        break;
    }
    case DLL_PROCESS_DETACH:
    {
        // Cleanup
        if (g_OffsetScanner) {
            g_OffsetScanner->Shutdown();
            g_OffsetScanner.reset();
        }

        // Free console if created
        #ifdef _DEBUG
        FreeConsole();
        #endif

        break;
    }
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
        break;
    }
    return TRUE;
}

void PerformAutoScan() {
    try {
        // Wait a bit for the process to fully load
        std::this_thread::sleep_for(std::chrono::seconds(2));

        // Initialize scanner
        if (!InitializeScanner()) {
            OutputDebugStringA("[FiveMOffsets] Failed to initialize scanner\n");
            return;
        }

        OutputDebugStringA("[FiveMOffsets] Scanner initialized successfully\n");

        // Perform scan
        if (PerformScan()) {
            OutputDebugStringA("[FiveMOffsets] Scan completed successfully\n");

            // Export results
            if (ExportResults()) {
                OutputDebugStringA("[FiveMOffsets] Results exported to results/offsets/\n");
            } else {
                OutputDebugStringA("[FiveMOffsets] Failed to export results\n");
            }
        } else {
            OutputDebugStringA("[FiveMOffsets] Scan failed\n");
        }
    }
    catch (const std::exception& e) {
        std::string error_msg = "[FiveMOffsets] Exception during auto scan: ";
        error_msg += e.what();
        error_msg += "\n";
        OutputDebugStringA(error_msg.c_str());
    }
    catch (...) {
        OutputDebugStringA("[FiveMOffsets] Unknown exception during auto scan\n");
    }
}

void CreateConsoleWindow() {
    if (AllocConsole()) {
        FILE* pCout;
        FILE* pCin;
        FILE* pCerr;

        freopen_s(&pCout, "CONOUT$", "w", stdout);
        freopen_s(&pCin, "CONIN$", "r", stdin);
        freopen_s(&pCerr, "CONOUT$", "w", stderr);

        SetConsoleTitleA("FiveM Offset Scanner - Debug Console");

        // Make cout, wcout, cin, wcin, wcerr, cerr, wclog and clog
        // point to console as well
        std::ios::sync_with_stdio(true);
        std::wcout.clear();
        std::cout.clear();
        std::wcerr.clear();
        std::cerr.clear();
        std::wcin.clear();
        std::cin.clear();
    }
}

void PrintWelcomeMessage() {
    const char* welcome_msg =
        "[FiveMOffsets] ========================================\n"
        "[FiveMOffsets] FiveM Offset Scanner v1.0\n"
        "[FiveMOffsets] Advanced Pattern Scanning & Structure Detection\n"
        "[FiveMOffsets] ========================================\n"
        "[FiveMOffsets] Features:\n"
        "[FiveMOffsets] ✅ Pattern scans for hooking\n"
        "[FiveMOffsets] ✅ Offsets and RVA for key structures\n"
        "[FiveMOffsets] ✅ JSON exports for easy automation\n"
        "[FiveMOffsets] ✅ Automatic results export to results/offsets/\n"
        "[FiveMOffsets] ========================================\n"
        "[FiveMOffsets] Starting automatic scan...\n";

    OutputDebugStringA(welcome_msg);

    #ifdef _DEBUG
    std::cout << welcome_msg;
    #endif
}

