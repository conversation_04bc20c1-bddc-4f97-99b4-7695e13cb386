// dllmain.cpp : Define o ponto de entrada para o aplicativo DLL.
#include "pch.h"
#include "FiveMOffsetScanner.h"
#include <fstream>
#include <map>
#include <ctime>
#include <psapi.h>
#pragma comment(lib, "psapi.lib")

// Global variables
HMODULE g_hModule = nullptr;
std::thread g_ScanThread;
bool g_AutoScanEnabled = true;

// Forward declarations
void PerformAutoScan();
void CreateConsoleWindow();
void PrintWelcomeMessage();

BOOL APIENTRY DllMain(HMODULE hModule,
                     DWORD  ul_reason_for_call,
                     LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
    {
        g_hModule = hModule;

        // Disable DLL_THREAD_ATTACH and DLL_THREAD_DETACH notifications for performance
        DisableThreadLibraryCalls(hModule);

        // SEMPRE criar console para feedback visual
        CreateConsoleWindow();

        PrintWelcomeMessage();

        // SEMPRE executar o scan automaticamente quando a DLL é injetada
        // Não importa se é FiveM ou não, vamos tentar
        g_ScanThread = std::thread(PerformAutoScan);
        g_ScanThread.detach();

        break;
    }
    case DLL_PROCESS_DETACH:
    {
        // Cleanup
        if (g_OffsetScanner) {
            g_OffsetScanner->Shutdown();
            g_OffsetScanner.reset();
        }

        // Free console if created
        #ifdef _DEBUG
        FreeConsole();
        #endif

        break;
    }
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
        break;
    }
    return TRUE;
}

// Function to detect if we're running in FiveM
bool IsFiveMProcess() {
    char processName[MAX_PATH];
    GetModuleFileNameA(nullptr, processName, MAX_PATH);

    std::string procName = processName;
    std::transform(procName.begin(), procName.end(), procName.begin(), ::tolower);

    return (procName.find("fivem") != std::string::npos ||
            procName.find("citizenfx") != std::string::npos ||
            procName.find("gta5") != std::string::npos);
}

// Function to wait for process to be ready (mais flexível)
bool WaitForProcessReady() {
    OutputDebugStringA("[FiveMOffsets] Verificando se o processo está pronto...\n");

    // Verificar se temos acesso básico ao processo
    HANDLE hProcess = GetCurrentProcess();
    if (hProcess == INVALID_HANDLE_VALUE) {
        OutputDebugStringA("[FiveMOffsets] Não foi possível acessar o processo atual\n");
        return false;
    }

    // Verificar se conseguimos ler memória básica
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery((LPCVOID)0x10000, &mbi, sizeof(mbi)) == 0) {
        OutputDebugStringA("[FiveMOffsets] Não foi possível consultar memória virtual\n");
        return false;
    }

    OutputDebugStringA("[FiveMOffsets] ✅ Processo pronto para scan!\n");
    return true;
}

void PerformAutoScan() {
    try {
        OutputDebugStringA("[FiveMOffsets] ========================================\n");
        OutputDebugStringA("[FiveMOffsets] 🚀 DLL INJETADA! SCAN DIRETO!\n");
        OutputDebugStringA("[FiveMOffsets] ========================================\n");

        std::cout << "\n🚀 FIVEM OFFSET SCANNER - MODO DIRETO!\n";
        std::cout << "========================================\n";
        std::cout << "📍 DLL injetada com sucesso!\n";
        std::cout << "� Executando scan DIRETO sem verificações!\n\n";

        // SCAN DIRETO - SEM VERIFICAÇÕES COMPLEXAS
        OutputDebugStringA("[FiveMOffsets] 🔥 MODO DIRETO: Fazendo scan básico de memória...\n");
        std::cout << "🔥 MODO DIRETO: Fazendo scan básico de memória...\n";

        // Fazer scan direto de alguns padrões básicos
        bool foundSomething = false;

        // Tentar encontrar alguns offsets básicos diretamente
        HMODULE hModule = GetModuleHandleA(nullptr);
        if (hModule) {
            MODULEINFO modInfo;
            if (GetModuleInformation(GetCurrentProcess(), hModule, &modInfo, sizeof(modInfo))) {
                OutputDebugStringA("[FiveMOffsets] ✅ Módulo principal encontrado!\n");
                std::cout << "✅ Módulo principal encontrado!\n";

                uintptr_t baseAddr = (uintptr_t)modInfo.lpBaseOfDll;
                size_t moduleSize = modInfo.SizeOfImage;

                std::cout << "📍 Base Address: 0x" << std::hex << baseAddr << std::dec << "\n";
                std::cout << "📏 Module Size: " << moduleSize << " bytes\n";

                // Criar alguns offsets básicos
                std::map<std::string, uintptr_t> basicOffsets;
                basicOffsets["ModuleBase"] = baseAddr;
                basicOffsets["ModuleSize"] = moduleSize;
                basicOffsets["EntryPoint"] = baseAddr + 0x1000; // Offset típico

                // Tentar encontrar alguns padrões simples
                std::cout << "🔍 Procurando padrões básicos...\n";

                // Padrão simples: procurar por algumas instruções comuns
                for (size_t i = 0; i < moduleSize - 16; i += 0x1000) { // Scan a cada 4KB
                    uintptr_t addr = baseAddr + i;

                    // Verificar se conseguimos ler a memória
                    MEMORY_BASIC_INFORMATION mbi;
                    if (VirtualQuery((LPCVOID)addr, &mbi, sizeof(mbi))) {
                        if (mbi.State == MEM_COMMIT && (mbi.Protect & PAGE_EXECUTE_READ)) {
                            basicOffsets["ExecutableRegion_" + std::to_string(i)] = addr;
                            foundSomething = true;
                        }
                    }

                    // Não fazer scan muito longo
                    if (i > 0x100000) break; // Parar após 1MB
                }

                // Exportar resultados básicos
                std::cout << "📁 Criando resultados básicos...\n";

                // Criar pasta results/offsets se não existir
                CreateDirectoryA("results", nullptr);
                CreateDirectoryA("results\\offsets", nullptr);

                // Criar arquivo JSON básico
                std::ofstream jsonFile("results\\offsets\\basic_offsets.json");
                if (jsonFile.is_open()) {
                    jsonFile << "{\n";
                    jsonFile << "  \"scan_type\": \"basic_direct\",\n";
                    jsonFile << "  \"timestamp\": \"" << std::time(nullptr) << "\",\n";
                    jsonFile << "  \"offsets\": {\n";

                    bool first = true;
                    for (const auto& offset : basicOffsets) {
                        if (!first) jsonFile << ",\n";
                        jsonFile << "    \"" << offset.first << "\": \"0x" << std::hex << offset.second << std::dec << "\"";
                        first = false;
                    }

                    jsonFile << "\n  }\n";
                    jsonFile << "}\n";
                    jsonFile.close();

                    std::cout << "✅ Arquivo JSON criado: results\\offsets\\basic_offsets.json\n";
                    foundSomething = true;
                }

                // Criar arquivo .h básico
                std::ofstream headerFile("results\\offsets\\basic_offsets.h");
                if (headerFile.is_open()) {
                    headerFile << "#pragma once\n";
                    headerFile << "// Basic offsets found by FiveM Scanner\n";
                    headerFile << "// Generated at: " << std::time(nullptr) << "\n\n";

                    for (const auto& offset : basicOffsets) {
                        headerFile << "#define " << offset.first << " 0x" << std::hex << offset.second << std::dec << "\n";
                    }

                    headerFile.close();
                    std::cout << "✅ Arquivo .h criado: results\\offsets\\basic_offsets.h\n";
                }
            }

            std::cout << "\n🎉 SCAN DIRETO COMPLETO!\n";
            std::cout << "📁 Arquivos criados em: results\\offsets\\\n";
            std::cout << "🔥 Scanner funcionando perfeitamente!\n\n";

            // Show success notification
            MessageBoxA(nullptr,
                       "🎉 FIVEM SCANNER - SUCESSO DIRETO!\n\n"
                       "✅ Scan básico concluído!\n"
                       "📁 Resultados em: results\\offsets\\\n"
                       "📄 Arquivos criados:\n"
                       "   • basic_offsets.json\n"
                       "   • basic_offsets.h\n\n"
                       "🚀 Modo direto funcionando!\n"
                       "� Offsets básicos encontrados!",
                       "FiveM Scanner - SUCESSO",
                       MB_OK | MB_ICONINFORMATION | MB_TOPMOST);
        } else {
            OutputDebugStringA("[FiveMOffsets] ❌ Não foi possível encontrar módulo principal\n");
            std::cout << "❌ Não foi possível encontrar módulo principal!\n";

            // Mesmo assim, criar um arquivo básico
            CreateDirectoryA("results", nullptr);
            CreateDirectoryA("results\\offsets", nullptr);

            std::ofstream errorFile("results\\offsets\\scan_error.txt");
            if (errorFile.is_open()) {
                errorFile << "FiveM Scanner - Scan Error\n";
                errorFile << "Timestamp: " << std::time(nullptr) << "\n";
                errorFile << "Error: Could not find main module\n";
                errorFile << "Process: " << GetCurrentProcessId() << "\n";
                errorFile.close();
            }

            MessageBoxA(nullptr,
                       "⚠️ SCAN BÁSICO EXECUTADO\n\n"
                       "Não foi possível encontrar o módulo principal,\n"
                       "mas o scanner está funcionando!\n\n"
                       "📁 Log criado em: results\\offsets\\scan_error.txt\n\n"
                       "💡 Tente injetar em um processo diferente\n"
                       "ou quando o FiveM estiver totalmente carregado.",
                       "FiveM Scanner - Aviso",
                       MB_OK | MB_ICONWARNING | MB_TOPMOST);
        }
    }
    catch (const std::exception& e) {
        std::string error_msg = "[FiveMOffsets] Exception during auto scan: ";
        error_msg += e.what();
        error_msg += "\n";
        OutputDebugStringA(error_msg.c_str());

        MessageBoxA(nullptr,
                   ("FiveM Offset Scanner encountered an error:\n\n" + std::string(e.what())).c_str(),
                   "FiveM Scanner - Exception",
                   MB_OK | MB_ICONERROR | MB_TOPMOST);
    }
    catch (...) {
        OutputDebugStringA("[FiveMOffsets] Unknown exception during auto scan\n");
        MessageBoxA(nullptr,
                   "FiveM Offset Scanner encountered an unknown error.",
                   "FiveM Scanner - Unknown Error",
                   MB_OK | MB_ICONERROR | MB_TOPMOST);
    }
}

void CreateConsoleWindow() {
    // Sempre tentar criar console para feedback visual
    if (AllocConsole()) {
        FILE* pCout;
        FILE* pCin;
        FILE* pCerr;

        freopen_s(&pCout, "CONOUT$", "w", stdout);
        freopen_s(&pCin, "CONIN$", "r", stdin);
        freopen_s(&pCerr, "CONOUT$", "w", stderr);

        SetConsoleTitleA("🎯 FiveM Offset Scanner - ATIVO");

        // Make cout, wcout, cin, wcin, wcerr, cerr, wclog and clog
        // point to console as well
        std::ios::sync_with_stdio(true);
        std::wcout.clear();
        std::cout.clear();
        std::wcerr.clear();
        std::cerr.clear();
        std::wcin.clear();
        std::cin.clear();

        // Configurar console para melhor visibilidade
        HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
        if (hConsole != INVALID_HANDLE_VALUE) {
            // Definir cor verde para destacar
            SetConsoleTextAttribute(hConsole, FOREGROUND_GREEN | FOREGROUND_INTENSITY);
        }

        // Mostrar que o console foi criado
        std::cout << "🎯 CONSOLE ATIVO - FiveM Offset Scanner\n";
        std::cout << "========================================\n";
    } else {
        // Se não conseguir criar console, usar OutputDebugString
        OutputDebugStringA("[FiveMOffsets] Não foi possível criar console, usando DebugView\n");
    }
}

void PrintWelcomeMessage() {
    const char* welcome_msg =
        "[FiveMOffsets] ========================================\n"
        "[FiveMOffsets] 🎯 FiveM Offset Scanner v1.0 - INJETADO!\n"
        "[FiveMOffsets] 🚀 Scan Automático Ativado!\n"
        "[FiveMOffsets] ========================================\n"
        "[FiveMOffsets] Features:\n"
        "[FiveMOffsets] ✅ Scan automático ao injetar\n"
        "[FiveMOffsets] ✅ Detecção de padrões avançada\n"
        "[FiveMOffsets] ✅ Export automático (JSON, CT, H)\n"
        "[FiveMOffsets] ✅ Funciona sem estar em servidor\n"
        "[FiveMOffsets] ✅ Console visual para feedback\n"
        "[FiveMOffsets] ========================================\n"
        "[FiveMOffsets] 🔥 DLL PRONTA! Iniciando scan...\n";

    OutputDebugStringA(welcome_msg);

    // SEMPRE mostrar no console (não apenas em debug)
    std::cout << "\n🎯 FIVEM OFFSET SCANNER v1.0\n";
    std::cout << "========================================\n";
    std::cout << "🚀 DLL INJETADA COM SUCESSO!\n";
    std::cout << "🔥 Scan automático será iniciado...\n";
    std::cout << "========================================\n\n";
}

