// dllmain.cpp : Define o ponto de entrada para o aplicativo DLL.
#include "pch.h"
#include "FiveMOffsetScanner.h"

// Global variables
HMODULE g_hModule = nullptr;
std::thread g_ScanThread;
bool g_AutoScanEnabled = true;

// Forward declarations
void PerformAutoScan();
void CreateConsoleWindow();
void PrintWelcomeMessage();

BOOL APIENTRY DllMain(HMODULE hModule,
                     DWORD  ul_reason_for_call,
                     LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
    {
        g_hModule = hModule;

        // Disable DLL_THREAD_ATTACH and DLL_THREAD_DETACH notifications for performance
        DisableThreadLibraryCalls(hModule);

        // Create console for debugging (optional)
        #ifdef _DEBUG
        CreateConsoleWindow();
        #endif

        PrintWelcomeMessage();

        // Initialize the scanner in a separate thread to avoid blocking DLL load
        if (g_AutoScanEnabled) {
            g_ScanThread = std::thread(PerformAutoScan);
            g_ScanThread.detach();
        }

        break;
    }
    case DLL_PROCESS_DETACH:
    {
        // Cleanup
        if (g_OffsetScanner) {
            g_OffsetScanner->Shutdown();
            g_OffsetScanner.reset();
        }

        // Free console if created
        #ifdef _DEBUG
        FreeConsole();
        #endif

        break;
    }
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
        break;
    }
    return TRUE;
}

// Function to detect if we're running in FiveM
bool IsFiveMProcess() {
    char processName[MAX_PATH];
    GetModuleFileNameA(nullptr, processName, MAX_PATH);

    std::string procName = processName;
    std::transform(procName.begin(), procName.end(), procName.begin(), ::tolower);

    return (procName.find("fivem") != std::string::npos ||
            procName.find("citizenfx") != std::string::npos ||
            procName.find("gta5") != std::string::npos);
}

// Function to wait for FiveM modules to be ready
bool WaitForFiveMReady() {
    OutputDebugStringA("[FiveMOffsets] Waiting for FiveM to be ready...\n");

    int attempts = 0;
    const int maxAttempts = 60; // 60 seconds max wait

    while (attempts < maxAttempts) {
        // Check if GTA5.exe module is loaded
        HMODULE hGTA = GetModuleHandleA("GTA5.exe");
        if (hGTA) {
            // Additional check: ensure the module is properly initialized
            MODULEINFO modInfo;
            if (GetModuleInformation(GetCurrentProcess(), hGTA, &modInfo, sizeof(modInfo))) {
                if (modInfo.SizeOfImage > 0x1000000) { // GTA5 should be > 16MB
                    OutputDebugStringA("[FiveMOffsets] GTA5.exe module detected and ready\n");
                    return true;
                }
            }
        }

        // Also check for other FiveM-specific modules
        HMODULE hCitizenFX = GetModuleHandleA("CitizenFX.exe");
        if (hCitizenFX) {
            OutputDebugStringA("[FiveMOffsets] CitizenFX.exe module detected\n");
            return true;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        attempts++;

        if (attempts % 10 == 0) {
            OutputDebugStringA("[FiveMOffsets] Still waiting for FiveM modules...\n");
        }
    }

    OutputDebugStringA("[FiveMOffsets] Timeout waiting for FiveM modules\n");
    return false;
}

void PerformAutoScan() {
    try {
        // Check if we're in a FiveM process
        if (!IsFiveMProcess()) {
            OutputDebugStringA("[FiveMOffsets] Not running in FiveM process, scanner disabled\n");
            return;
        }

        OutputDebugStringA("[FiveMOffsets] FiveM process detected, starting automatic scan...\n");

        // Wait for FiveM to be ready
        if (!WaitForFiveMReady()) {
            OutputDebugStringA("[FiveMOffsets] FiveM not ready, aborting scan\n");
            return;
        }

        // Additional stabilization delay
        OutputDebugStringA("[FiveMOffsets] FiveM ready, waiting for stabilization...\n");
        std::this_thread::sleep_for(std::chrono::seconds(5));

        // Initialize scanner
        OutputDebugStringA("[FiveMOffsets] Initializing scanner...\n");
        if (!InitializeScanner()) {
            OutputDebugStringA("[FiveMOffsets] Failed to initialize scanner\n");
            return;
        }

        OutputDebugStringA("[FiveMOffsets] Scanner initialized successfully\n");

        // Perform scan
        OutputDebugStringA("[FiveMOffsets] Starting pattern scan...\n");
        if (PerformScan()) {
            OutputDebugStringA("[FiveMOffsets] Scan completed successfully\n");

            // Export results
            OutputDebugStringA("[FiveMOffsets] Exporting results...\n");
            if (ExportResults()) {
                OutputDebugStringA("[FiveMOffsets] ✅ Results exported to results/offsets/ folder\n");

                // Show success notification
                MessageBoxA(nullptr,
                           "FiveM Offset Scanner completed successfully!\n\n"
                           "✅ Offsets scanned and exported\n"
                           "📁 Check results/offsets/ folder\n"
                           "📄 Files: JSON, Cheat Engine, C++ headers\n\n"
                           "Scanner will continue running in background.",
                           "FiveM Scanner - Success",
                           MB_OK | MB_ICONINFORMATION | MB_TOPMOST);
            } else {
                OutputDebugStringA("[FiveMOffsets] ❌ Failed to export results\n");
                MessageBoxA(nullptr,
                           "FiveM Offset Scanner completed but failed to export results.\n\n"
                           "Check if the results/offsets/ folder is writable.",
                           "FiveM Scanner - Export Error",
                           MB_OK | MB_ICONWARNING | MB_TOPMOST);
            }
        } else {
            OutputDebugStringA("[FiveMOffsets] ❌ Scan failed\n");
            MessageBoxA(nullptr,
                       "FiveM Offset Scanner failed to complete scan.\n\n"
                       "This might happen if:\n"
                       "• FiveM is not fully loaded\n"
                       "• Game is running in a different mode\n"
                       "• Memory protection is blocking access",
                       "FiveM Scanner - Scan Error",
                       MB_OK | MB_ICONERROR | MB_TOPMOST);
        }
    }
    catch (const std::exception& e) {
        std::string error_msg = "[FiveMOffsets] Exception during auto scan: ";
        error_msg += e.what();
        error_msg += "\n";
        OutputDebugStringA(error_msg.c_str());

        MessageBoxA(nullptr,
                   ("FiveM Offset Scanner encountered an error:\n\n" + std::string(e.what())).c_str(),
                   "FiveM Scanner - Exception",
                   MB_OK | MB_ICONERROR | MB_TOPMOST);
    }
    catch (...) {
        OutputDebugStringA("[FiveMOffsets] Unknown exception during auto scan\n");
        MessageBoxA(nullptr,
                   "FiveM Offset Scanner encountered an unknown error.",
                   "FiveM Scanner - Unknown Error",
                   MB_OK | MB_ICONERROR | MB_TOPMOST);
    }
}

void CreateConsoleWindow() {
    if (AllocConsole()) {
        FILE* pCout;
        FILE* pCin;
        FILE* pCerr;

        freopen_s(&pCout, "CONOUT$", "w", stdout);
        freopen_s(&pCin, "CONIN$", "r", stdin);
        freopen_s(&pCerr, "CONOUT$", "w", stderr);

        SetConsoleTitleA("FiveM Offset Scanner - Debug Console");

        // Make cout, wcout, cin, wcin, wcerr, cerr, wclog and clog
        // point to console as well
        std::ios::sync_with_stdio(true);
        std::wcout.clear();
        std::cout.clear();
        std::wcerr.clear();
        std::cerr.clear();
        std::wcin.clear();
        std::cin.clear();
    }
}

void PrintWelcomeMessage() {
    const char* welcome_msg =
        "[FiveMOffsets] ========================================\n"
        "[FiveMOffsets] FiveM Offset Scanner v1.0\n"
        "[FiveMOffsets] Advanced Pattern Scanning & Structure Detection\n"
        "[FiveMOffsets] ========================================\n"
        "[FiveMOffsets] Features:\n"
        "[FiveMOffsets] ✅ Pattern scans for hooking\n"
        "[FiveMOffsets] ✅ Offsets and RVA for key structures\n"
        "[FiveMOffsets] ✅ JSON exports for easy automation\n"
        "[FiveMOffsets] ✅ Automatic results export to results/offsets/\n"
        "[FiveMOffsets] ========================================\n"
        "[FiveMOffsets] Starting automatic scan...\n";

    OutputDebugStringA(welcome_msg);

    #ifdef _DEBUG
    std::cout << welcome_msg;
    #endif
}

