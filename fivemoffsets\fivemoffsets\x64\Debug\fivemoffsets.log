﻿  FiveMOffsetScanner.cpp
  PatternScanner.cpp
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(157,5): error C2059: erro de sintaxe: 'if'
  dllmain.cpp
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(157,68): error C2143: erro de sintaxe: ';' ausente antes de '{'
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(157,68): error C2447: '{': faltando cabeçalho de função (lista formal de estilo antigo?)
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(165,5): error C3927: '->': o tipo de retorno à direita não é permitido após um declarador que não é função
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(165,12): error C3484: erro de sintaxe: esperado '->' antes do tipo de retorno
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(165,13): error C3613: tipo de retorno ausente após '->' ('int' assumido)
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(165,5): error C4430: faltando especificador de tipo - int assumido. Observação: C++ não suporta default-int
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(165,13): error C2146: erro de sintaxe: ';' ausente antes do identificador 'scan_duration'
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(168,5): error C2059: erro de sintaxe: 'if'
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(168,23): error C2143: erro de sintaxe: ';' ausente antes de '{'
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(168,23): error C2447: '{': faltando cabeçalho de função (lista formal de estilo antigo?)
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(185,7): error C2059: erro de sintaxe: 'else'
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(185,12): error C2143: erro de sintaxe: ';' ausente antes de '{'
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(185,12): error C2447: '{': faltando cabeçalho de função (lista formal de estilo antigo?)
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(189,5): error C4430: faltando especificador de tipo - int assumido. Observação: C++ não suporta default-int
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(190,5): error C2059: erro de sintaxe: 'return'
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(191,1): error C2059: erro de sintaxe: '}'
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(191,1): error C2143: erro de sintaxe: ';' ausente antes de '}'
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(193,41): error C2143: erro de sintaxe: ';' ausente antes de '{'
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(193,41): error C2447: '{': faltando cabeçalho de função (lista formal de estilo antigo?)
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\dllmain.cpp(216,36): warning C4244: 'inicializando': conversão de '_Rep' para 'int', possível perda de dados
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\dllmain.cpp(216,36): warning C4244:         with
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\dllmain.cpp(216,36): warning C4244:         [
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\dllmain.cpp(216,36): warning C4244:             _Rep=__int64
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\dllmain.cpp(216,36): warning C4244:         ]
  (compilando o arquivo fonte '/dllmain.cpp')
  
  Gerando Código...
