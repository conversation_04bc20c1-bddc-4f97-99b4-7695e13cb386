﻿  pch.cpp
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\framework.h(3,9): warning C4005: 'WIN32_LEAN_AND_MEAN': redefinição de macro
  (compilando o arquivo fonte 'pch.cpp')
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\framework.h(3,9):
      'WIN32_LEAN_AND_MEAN' declarado anteriormente na linha de comando
  
  Warning: Using Detours fallback implementation. Hooking functionality will be disabled.
  To enable hooking, install Microsoft Detours and define DETOURS_AVAILABLE
  dllmain.cpp
  FileManager.cpp
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(138,32): error C2039: ' parse': não é um membro de 'nlohmann::json'
  (compilando o arquivo fonte '/FileManager.cpp')
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(11,11):
      consulte a declaração de 'nlohmann::json'
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(138,32): error C3861: 'parse': identificador não encontrado
  (compilando o arquivo fonte '/FileManager.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,29): error C2679: '=' binário : nenhum operador encontrado que receba um operando de lado direito do tipo 'initializer list' (ou não há conversão aceitável)
  (compilando o arquivo fonte '/FileManager.cpp')
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(277,5):
      poderia ser 'nlohmann::json &nlohmann::json::operator =(nlohmann::json &&)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,29):
          'nlohmann::json &nlohmann::json::operator =(nlohmann::json &&)': não é possível converter um argumento 2 de 'initializer list' em 'nlohmann::json &&'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
              Razão: não é possível converter de 'initializer list' para 'nlohmann::json'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
              'nlohmann::json::json': função não recebe 5 argumentos
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
                  ao tentar corresponder a lista de argumentos '(initializer list, initializer list, initializer list, initializer list, initializer list)'
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(277,5):
      ou       'nlohmann::json &nlohmann::json::operator =(const nlohmann::json &)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,29):
          'nlohmann::json &nlohmann::json::operator =(const nlohmann::json &)': não é possível converter um argumento 2 de 'initializer list' em 'const nlohmann::json &'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
              Razão: não é possível converter de 'initializer list' para 'const nlohmann::json'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
              'nlohmann::json::json': função não recebe 5 argumentos
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
                  ao tentar corresponder a lista de argumentos '(initializer list, initializer list, initializer list, initializer list, initializer list)'
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(83,15):
      ou       'nlohmann::json &nlohmann::json::operator =(double)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,29):
          'nlohmann::json &nlohmann::json::operator =(double)': não é possível converter um argumento 2 de 'initializer list' em 'double'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
              O inicializador contém elementos em excesso
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(77,15):
      ou       'nlohmann::json &nlohmann::json::operator =(uint64_t)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,29):
          'nlohmann::json &nlohmann::json::operator =(uint64_t)': não é possível converter um argumento 2 de 'initializer list' em 'uint64_t'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
              O inicializador contém elementos em excesso
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(71,15):
      ou       'nlohmann::json &nlohmann::json::operator =(int64_t)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,29):
          'nlohmann::json &nlohmann::json::operator =(int64_t)': não é possível converter um argumento 2 de 'initializer list' em 'int64_t'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
              O inicializador contém elementos em excesso
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(65,15):
      ou       'nlohmann::json &nlohmann::json::operator =(int)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,29):
          'nlohmann::json &nlohmann::json::operator =(int)': não é possível converter um argumento 2 de 'initializer list' em 'int'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
              O inicializador contém elementos em excesso
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(59,15):
      ou       'nlohmann::json &nlohmann::json::operator =(bool)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,29):
          'nlohmann::json &nlohmann::json::operator =(bool)': não é possível converter um argumento 2 de 'initializer list' em 'bool'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
              O inicializador contém elementos em excesso
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(53,15):
      ou       'nlohmann::json &nlohmann::json::operator =(const std::string &)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,29):
          'nlohmann::json &nlohmann::json::operator =(const std::string &)': não é possível converter um argumento 2 de 'initializer list' em 'const std::string &'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
              Razão: não é possível converter de 'initializer list' para 'const std::string'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
              'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::initializer_list<_Elem>,const _Alloc &)': não é possível converter um argumento 1 de 'initializer list' em 'std::initializer_list<_Elem>'
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
          and
          [
              _Elem=char
          ]
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
                  Elemento '1': nenhuma conversão de 'initializer list' em '_Elem'
          with
          [
              _Elem=char
          ]
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
                  Elemento '2': nenhuma conversão de 'initializer list' em '_Elem'
          with
          [
              _Elem=char
          ]
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
                  Elemento '3': nenhuma conversão de 'initializer list' em '_Elem'
          with
          [
              _Elem=char
          ]
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
                  Elemento '4': nenhuma conversão de 'initializer list' em '_Elem'
          with
          [
              _Elem=char
          ]
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
                  Elemento '5': nenhuma conversão de 'initializer list' em '_Elem'
          with
          [
              _Elem=char
          ]
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,33):
                  ao tentar corresponder a lista de argumentos '(initializer list)'
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FileManager.cpp(267,29):
      ao tentar corresponder a lista de argumentos '(nlohmann::json, initializer list)'
  
  FiveMOffsetScanner.cpp
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,28): error C2679: '=' binário : nenhum operador encontrado que receba um operando de lado direito do tipo 'initializer list' (ou não há conversão aceitável)
  (compilando o arquivo fonte '/FiveMOffsetScanner.cpp')
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(277,5):
      poderia ser 'nlohmann::json &nlohmann::json::operator =(nlohmann::json &&)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,28):
          'nlohmann::json &nlohmann::json::operator =(nlohmann::json &&)': não é possível converter um argumento 2 de 'initializer list' em 'nlohmann::json &&'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
              Razão: não é possível converter de 'initializer list' para 'nlohmann::json'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
              'nlohmann::json::json': função não recebe 5 argumentos
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
                  ao tentar corresponder a lista de argumentos '(initializer list, initializer list, initializer list, initializer list, initializer list)'
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(277,5):
      ou       'nlohmann::json &nlohmann::json::operator =(const nlohmann::json &)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,28):
          'nlohmann::json &nlohmann::json::operator =(const nlohmann::json &)': não é possível converter um argumento 2 de 'initializer list' em 'const nlohmann::json &'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
              Razão: não é possível converter de 'initializer list' para 'const nlohmann::json'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
              'nlohmann::json::json': função não recebe 5 argumentos
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
                  ao tentar corresponder a lista de argumentos '(initializer list, initializer list, initializer list, initializer list, initializer list)'
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(83,15):
      ou       'nlohmann::json &nlohmann::json::operator =(double)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,28):
          'nlohmann::json &nlohmann::json::operator =(double)': não é possível converter um argumento 2 de 'initializer list' em 'double'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
              O inicializador contém elementos em excesso
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(77,15):
      ou       'nlohmann::json &nlohmann::json::operator =(uint64_t)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,28):
          'nlohmann::json &nlohmann::json::operator =(uint64_t)': não é possível converter um argumento 2 de 'initializer list' em 'uint64_t'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
              O inicializador contém elementos em excesso
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(71,15):
      ou       'nlohmann::json &nlohmann::json::operator =(int64_t)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,28):
          'nlohmann::json &nlohmann::json::operator =(int64_t)': não é possível converter um argumento 2 de 'initializer list' em 'int64_t'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
              O inicializador contém elementos em excesso
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(65,15):
      ou       'nlohmann::json &nlohmann::json::operator =(int)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,28):
          'nlohmann::json &nlohmann::json::operator =(int)': não é possível converter um argumento 2 de 'initializer list' em 'int'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
              O inicializador contém elementos em excesso
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(59,15):
      ou       'nlohmann::json &nlohmann::json::operator =(bool)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,28):
          'nlohmann::json &nlohmann::json::operator =(bool)': não é possível converter um argumento 2 de 'initializer list' em 'bool'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
              O inicializador contém elementos em excesso
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(53,15):
      ou       'nlohmann::json &nlohmann::json::operator =(const std::string &)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,28):
          'nlohmann::json &nlohmann::json::operator =(const std::string &)': não é possível converter um argumento 2 de 'initializer list' em 'const std::string &'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
              Razão: não é possível converter de 'initializer list' para 'const std::string'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
              'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::initializer_list<_Elem>,const _Alloc &)': não é possível converter um argumento 1 de 'initializer list' em 'std::initializer_list<_Elem>'
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
          and
          [
              _Elem=char
          ]
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
                  Elemento '1': nenhuma conversão de 'initializer list' em '_Elem'
          with
          [
              _Elem=char
          ]
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
                  Elemento '2': nenhuma conversão de 'initializer list' em '_Elem'
          with
          [
              _Elem=char
          ]
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
                  Elemento '3': nenhuma conversão de 'initializer list' em '_Elem'
          with
          [
              _Elem=char
          ]
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
                  Elemento '4': nenhuma conversão de 'initializer list' em '_Elem'
          with
          [
              _Elem=char
          ]
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
                  Elemento '5': nenhuma conversão de 'initializer list' em '_Elem'
          with
          [
              _Elem=char
          ]
                  C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,32):
                  ao tentar corresponder a lista de argumentos '(initializer list)'
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMOffsetScanner.cpp(198,28):
      ao tentar corresponder a lista de argumentos '(nlohmann::json, initializer list)'
  
  FiveMStructures.cpp
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\FiveMStructures.cpp(234,51): error C2064: o termo não pode ser avaliado como uma função recebendo 0 argumentos
  (compilando o arquivo fonte '/FiveMStructures.cpp')
  
  HookManager.cpp
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(245,49): error C2064: o termo não pode ser avaliado como uma função recebendo 0 argumentos
  (compilando o arquivo fonte '/HookManager.cpp')
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(255,20): error C2039: ' push_back': não é um membro de 'nlohmann::json'
  (compilando o arquivo fonte '/HookManager.cpp')
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(11,11):
      consulte a declaração de 'nlohmann::json'
  
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,22): error C2679: '=' binário : nenhum operador encontrado que receba um operando de lado direito do tipo 'const std::vector<std::string,std::allocator<std::string>>' (ou não há conversão aceitável)
  (compilando o arquivo fonte '/HookManager.cpp')
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(277,5):
      poderia ser 'nlohmann::json &nlohmann::json::operator =(nlohmann::json &&)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,22):
          'nlohmann::json &nlohmann::json::operator =(nlohmann::json &&)': não é possível converter um argumento 2 de 'const std::vector<std::string,std::allocator<std::string>>' em 'nlohmann::json &&'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,26):
              Razão: não é possível converter de 'const std::vector<std::string,std::allocator<std::string>>' para 'nlohmann::json'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,26):
              Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(277,5):
      ou       'nlohmann::json &nlohmann::json::operator =(const nlohmann::json &)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,22):
          'nlohmann::json &nlohmann::json::operator =(const nlohmann::json &)': não é possível converter um argumento 2 de 'const std::vector<std::string,std::allocator<std::string>>' em 'const nlohmann::json &'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,26):
              Razão: não é possível converter de 'const std::vector<std::string,std::allocator<std::string>>' para 'const nlohmann::json'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,26):
              Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(83,15):
      ou       'nlohmann::json &nlohmann::json::operator =(double)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,22):
          'nlohmann::json &nlohmann::json::operator =(double)': não é possível converter um argumento 2 de 'const std::vector<std::string,std::allocator<std::string>>' em 'double'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,26):
              Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(77,15):
      ou       'nlohmann::json &nlohmann::json::operator =(uint64_t)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,22):
          'nlohmann::json &nlohmann::json::operator =(uint64_t)': não é possível converter um argumento 2 de 'const std::vector<std::string,std::allocator<std::string>>' em 'uint64_t'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,26):
              Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(71,15):
      ou       'nlohmann::json &nlohmann::json::operator =(int64_t)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,22):
          'nlohmann::json &nlohmann::json::operator =(int64_t)': não é possível converter um argumento 2 de 'const std::vector<std::string,std::allocator<std::string>>' em 'int64_t'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,26):
              Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(65,15):
      ou       'nlohmann::json &nlohmann::json::operator =(int)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,22):
          'nlohmann::json &nlohmann::json::operator =(int)': não é possível converter um argumento 2 de 'const std::vector<std::string,std::allocator<std::string>>' em 'int'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,26):
              Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(59,15):
      ou       'nlohmann::json &nlohmann::json::operator =(bool)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,22):
          'nlohmann::json &nlohmann::json::operator =(bool)': não é possível converter um argumento 2 de 'const std::vector<std::string,std::allocator<std::string>>' em 'bool'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,26):
              Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\json_fallback.h(53,15):
      ou       'nlohmann::json &nlohmann::json::operator =(const std::string &)'
          C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,22):
          'nlohmann::json &nlohmann::json::operator =(const std::string &)': não é possível converter um argumento 2 de 'const std::vector<std::string,std::allocator<std::string>>' em 'const std::string &'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,26):
              Razão: não é possível converter de 'const std::vector<std::string,std::allocator<std::string>>' para 'const std::string'
              C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,26):
              Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\HookManager.cpp(259,22):
      ao tentar corresponder a lista de argumentos '(nlohmann::json, const std::vector<std::string,std::allocator<std::string>>)'
  
  PatternScanner.cpp
  Gerando Código...
