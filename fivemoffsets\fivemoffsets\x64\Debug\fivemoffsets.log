﻿  pch.cpp
C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\framework.h(3,9): warning C4005: 'WIN32_LEAN_AND_MEAN': redefinição de macro
  (compilando o arquivo fonte 'pch.cpp')
      C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\fivemoffsets\framework.h(3,9):
      'WIN32_LEAN_AND_MEAN' declarado anteriormente na linha de comando
  
  Warning: Using Detours fallback implementation. Hooking functionality will be disabled.
  To enable hooking, install Microsoft Detours and define DETOURS_AVAILABLE
  dllmain.cpp
  FileManager.cpp
  FiveMOffsetScanner.cpp
  FiveMStructures.cpp
  HookManager.cpp
  PatternScanner.cpp
  Gerando Código...
     Criando biblioteca C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\x64\Debug\fivemoffsets.lib e objeto C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\x64\Debug\fivemoffsets.exp
  fivemoffsets.vcxproj -> C:\Users\<USER>\Desktop\fivem offsets\fivemoffsets\x64\Debug\fivemoffsets.dll
