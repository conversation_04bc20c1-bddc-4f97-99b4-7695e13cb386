#pragma once
#include "framework.h"
#include "PatternScanner.h"

class HookManager {
public:
    struct HookInfo {
        std::string name;
        std::string description;
        uintptr_t original_address;
        uintptr_t hook_address;
        void* original_function;
        bool is_enabled;
        std::string category;
        
        HookInfo() : original_address(0), hook_address(0), original_function(nullptr), is_enabled(false) {}
        HookInfo(const std::string& n, const std::string& desc, uintptr_t orig_addr, 
                uintptr_t hook_addr, const std::string& cat)
            : name(n), description(desc), original_address(orig_addr), hook_address(hook_addr),
              original_function(nullptr), is_enabled(false), category(cat) {}
    };

    // Hook callback types
    using VoidHook = void(*)();
    using IntHook = int(*)();
    using BoolHook = bool(*)();
    using PtrHook = void*(*)();

private:
    std::unique_ptr<PatternScanner> m_scanner;
    std::map<std::string, HookInfo> m_hooks;
    std::vector<std::string> m_enabled_hooks;
    bool m_is_initialized;

public:
    HookManager();
    ~HookManager();

    // Initialization
    bool Initialize();
    void Shutdown();
    bool IsInitialized() const { return m_is_initialized; }

    // Hook management
    bool AddHook(const std::string& name, const std::string& description, 
                const std::string& pattern, void* hook_function, const std::string& category = "General");
    bool AddHook(const std::string& name, const std::string& description, 
                uintptr_t target_address, void* hook_function, const std::string& category = "General");
    
    bool EnableHook(const std::string& name);
    bool DisableHook(const std::string& name);
    bool IsHookEnabled(const std::string& name) const;
    
    void EnableAllHooks();
    void DisableAllHooks();
    
    // Hook information
    HookInfo GetHookInfo(const std::string& name) const;
    std::vector<HookInfo> GetAllHooks() const;
    std::vector<HookInfo> GetHooksByCategory(const std::string& category) const;
    
    // Original function access
    template<typename T>
    T GetOriginalFunction(const std::string& name) const;
    
    // Export functions
    nlohmann::json ExportToJson() const;
    bool ExportToFile(const std::string& filename) const;

private:
    void InitializeCommonHooks();
    bool CreateHook(const std::string& name, uintptr_t target_address, void* hook_function);
    void RemoveHook(const std::string& name);
};

// Hook function declarations for common FiveM functions
namespace FiveMHooks {
    // Player hooks
    extern "C" void __stdcall PlayerSpawnHook();
    extern "C" void __stdcall PlayerDeathHook();
    extern "C" void __stdcall PlayerDamageHook();
    
    // Vehicle hooks
    extern "C" void __stdcall VehicleSpawnHook();
    extern "C" void __stdcall VehicleDamageHook();
    extern "C" void __stdcall VehicleDestroyHook();
    
    // Weapon hooks
    extern "C" void __stdcall WeaponFireHook();
    extern "C" void __stdcall WeaponReloadHook();
    
    // Network hooks
    extern "C" void __stdcall NetworkSendHook();
    extern "C" void __stdcall NetworkReceiveHook();
    
    // Script hooks
    extern "C" void __stdcall ScriptExecuteHook();
    extern "C" void __stdcall NativeCallHook();
    
    // Memory hooks
    extern "C" void* __stdcall MallocHook(size_t size);
    extern "C" void __stdcall FreeHook(void* ptr);
    
    // Graphics hooks
    extern "C" void __stdcall RenderHook();
    extern "C" void __stdcall SwapBuffersHook();
    
    // Input hooks
    extern "C" void __stdcall KeyboardInputHook();
    extern "C" void __stdcall MouseInputHook();
}

// Template implementation
template<typename T>
T HookManager::GetOriginalFunction(const std::string& name) const {
    auto it = m_hooks.find(name);
    if (it != m_hooks.end() && it->second.original_function != nullptr) {
        return reinterpret_cast<T>(it->second.original_function);
    }
    return nullptr;
}

// Utility macros for hook creation
#define DECLARE_HOOK(name, ret_type, ...) \
    typedef ret_type (*name##_t)(__VA_ARGS__); \
    extern name##_t Original##name;

#define IMPLEMENT_HOOK(name, ret_type, ...) \
    name##_t Original##name = nullptr;

#define CALL_ORIGINAL(name, ...) \
    (Original##name ? Original##name(__VA_ARGS__) : ret_type{})

// Common hook declarations
DECLARE_HOOK(PlayerSpawn, void, int playerId)
DECLARE_HOOK(PlayerDeath, void, int playerId, int killerId)
DECLARE_HOOK(VehicleSpawn, void*, int modelHash, float x, float y, float z)
DECLARE_HOOK(WeaponFire, bool, void* weapon, void* target)
DECLARE_HOOK(NetworkSend, bool, void* data, size_t size)
DECLARE_HOOK(ScriptExecute, void, void* script, void* context)
DECLARE_HOOK(NativeCall, void*, uint64_t hash, void* context)
DECLARE_HOOK(Render, void, void* device)
DECLARE_HOOK(KeyboardInput, bool, int key, bool pressed)

// Hook patterns for common functions
namespace HookPatterns {
    // Player function patterns
    constexpr const char* PLAYER_SPAWN_FUNC = "40 53 48 83 EC 20 48 8B D9 E8 ? ? ? ? 48 8B CB";
    constexpr const char* PLAYER_DEATH_FUNC = "48 89 5C 24 ? 48 89 6C 24 ? 48 89 74 24 ? 57 48 83 EC 20 41 8B E8";
    
    // Vehicle function patterns
    constexpr const char* VEHICLE_SPAWN_FUNC = "48 89 5C 24 ? 48 89 74 24 ? 57 48 83 EC 20 8B FA 8B F1";
    constexpr const char* VEHICLE_DAMAGE_FUNC = "40 53 48 83 EC 30 48 8B D9 F3 0F 11 4C 24 ?";
    
    // Weapon function patterns
    constexpr const char* WEAPON_FIRE_FUNC = "48 8B C4 48 89 58 08 48 89 68 10 48 89 70 18 48 89 78 20 41 56";
    constexpr const char* WEAPON_RELOAD_FUNC = "40 53 48 83 EC 20 48 8B D9 E8 ? ? ? ? 84 C0 74 1A";
    
    // Network function patterns
    constexpr const char* NETWORK_SEND_FUNC = "48 89 5C 24 ? 48 89 6C 24 ? 48 89 74 24 ? 57 41 54 41 55 41 56 41 57";
    constexpr const char* NETWORK_RECEIVE_FUNC = "40 55 53 56 57 41 54 41 55 41 56 41 57 48 8D AC 24 ? ? ? ?";
    
    // Script function patterns
    constexpr const char* SCRIPT_EXECUTE_FUNC = "48 89 5C 24 ? 48 89 6C 24 ? 48 89 74 24 ? 57 48 83 EC 20 48 8B F2";
    constexpr const char* NATIVE_CALL_FUNC = "48 89 5C 24 ? 48 89 6C 24 ? 48 89 74 24 ? 57 41 54 41 55 41 56 41 57 48 83 EC 20";
    
    // Graphics function patterns
    constexpr const char* RENDER_FUNC = "40 53 48 83 EC 20 48 8B D9 E8 ? ? ? ? 48 8B CB E8 ? ? ? ?";
    constexpr const char* SWAP_BUFFERS_FUNC = "40 53 48 83 EC 20 48 8B D9 48 8B 09 48 8B 01";
    
    // Input function patterns
    constexpr const char* KEYBOARD_INPUT_FUNC = "40 53 48 83 EC 20 8B D9 E8 ? ? ? ? 84 C0 75 13";
    constexpr const char* MOUSE_INPUT_FUNC = "48 89 5C 24 ? 48 89 6C 24 ? 48 89 74 24 ? 57 48 83 EC 20 8B FA";
}
