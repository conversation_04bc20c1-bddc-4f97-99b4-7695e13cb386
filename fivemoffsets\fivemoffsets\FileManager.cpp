#include "pch.h"
#include "FileManager.h"
#include <filesystem>
#include <ctime>

namespace fs = std::filesystem;

FileManager::FileManager() : m_is_initialized(false) {
    m_base_directory = FileUtils::GetExecutableDirectory();
}

FileManager::~FileManager() = default;

bool FileManager::Initialize(const std::string& base_dir) {
    if (m_is_initialized) {
        return true;
    }
    
    if (!base_dir.empty()) {
        m_base_directory = base_dir;
    }
    
    m_results_directory = FileUtils::JoinPath(m_base_directory, FileConstants::RESULTS_DIR);
    m_offsets_directory = FileUtils::JoinPath(m_results_directory, FileConstants::OFFSETS_DIR);
    
    if (!CreateDirectories()) {
        return false;
    }
    
    m_is_initialized = true;
    return true;
}

bool FileManager::CreateDirectories() {
    try {
        if (!DirectoryExists(m_results_directory)) {
            if (!CreateDirectory(m_results_directory)) {
                return false;
            }
        }
        
        if (!DirectoryExists(m_offsets_directory)) {
            if (!CreateDirectory(m_offsets_directory)) {
                return false;
            }
        }
        
        // Create additional directories
        std::string backups_dir = FileUtils::JoinPath(m_results_directory, FileConstants::BACKUPS_DIR);
        if (!DirectoryExists(backups_dir)) {
            CreateDirectory(backups_dir);
        }
        
        std::string templates_dir = FileUtils::JoinPath(m_results_directory, FileConstants::TEMPLATES_DIR);
        if (!DirectoryExists(templates_dir)) {
            CreateDirectory(templates_dir);
        }
        
        return true;
    }
    catch (const std::exception&) {
        return false;
    }
}

bool FileManager::DirectoryExists(const std::string& path) const {
    try {
        return fs::exists(path) && fs::is_directory(path);
    }
    catch (const std::exception&) {
        return false;
    }
}

bool FileManager::CreateDirectory(const std::string& path) const {
    try {
        return fs::create_directories(path);
    }
    catch (const std::exception&) {
        return false;
    }
}

bool FileManager::WriteFile(const std::string& filename, const std::string& content) const {
    try {
        std::string full_path = GetFullPath(filename);
        EnsureDirectoryExists(FileUtils::GetDirectoryFromPath(full_path));
        
        std::ofstream file(full_path, std::ios::binary);
        if (!file.is_open()) {
            return false;
        }
        
        file << content;
        file.close();
        return true;
    }
    catch (const std::exception&) {
        return false;
    }
}

bool FileManager::WriteJsonFile(const std::string& filename, const nlohmann::json& json) const {
    try {
        std::string content = m_config.pretty_print ? json.dump(4) : json.dump();
        return WriteFile(filename, content);
    }
    catch (const std::exception&) {
        return false;
    }
}

std::string FileManager::ReadFile(const std::string& filename) const {
    try {
        std::string full_path = GetFullPath(filename);
        std::ifstream file(full_path, std::ios::binary);
        if (!file.is_open()) {
            return "";
        }
        
        std::string content((std::istreambuf_iterator<char>(file)),
                           std::istreambuf_iterator<char>());
        file.close();
        return content;
    }
    catch (const std::exception&) {
        return "";
    }
}

nlohmann::json FileManager::ReadJsonFile(const std::string& filename) const {
    try {
        std::string content = ReadFile(filename);
        if (content.empty()) {
            return nlohmann::json{};
        }
        return nlohmann::json::parse(content);
    }
    catch (const std::exception&) {
        return nlohmann::json{};
    }
}

bool FileManager::FileExists(const std::string& filename) const {
    try {
        std::string full_path = GetFullPath(filename);
        return fs::exists(full_path) && fs::is_regular_file(full_path);
    }
    catch (const std::exception&) {
        return false;
    }
}

bool FileManager::DeleteFile(const std::string& filename) const {
    try {
        std::string full_path = GetFullPath(filename);
        return fs::remove(full_path);
    }
    catch (const std::exception&) {
        return false;
    }
}

std::vector<std::string> FileManager::ListFiles(const std::string& directory, const std::string& extension) const {
    std::vector<std::string> files;
    try {
        std::string full_dir = GetFullPath(directory);
        if (!DirectoryExists(full_dir)) {
            return files;
        }
        
        for (const auto& entry : fs::directory_iterator(full_dir)) {
            if (entry.is_regular_file()) {
                std::string filename = entry.path().filename().string();
                if (extension.empty() || FileUtils::GetFileExtension(filename) == extension) {
                    files.push_back(filename);
                }
            }
        }
    }
    catch (const std::exception&) {
        // Return empty vector on error
    }
    
    return files;
}

bool FileManager::ExportOffsets(const nlohmann::json& offsets_data) const {
    if (!m_is_initialized) {
        return false;
    }
    
    std::string filename = FileUtils::JoinPath(m_offsets_directory, FileConstants::OFFSETS_FILENAME);
    return WriteJsonFile(filename, offsets_data);
}

bool FileManager::ExportHooks(const nlohmann::json& hooks_data) const {
    if (!m_is_initialized) {
        return false;
    }
    
    std::string filename = FileUtils::JoinPath(m_offsets_directory, FileConstants::HOOKS_FILENAME);
    return WriteJsonFile(filename, hooks_data);
}

bool FileManager::ExportCombined(const nlohmann::json& offsets_data, const nlohmann::json& hooks_data) const {
    if (!m_is_initialized) {
        return false;
    }
    
    nlohmann::json combined;
    combined["metadata"] = CreateMetadata();
    combined["offsets"] = offsets_data;
    combined["hooks"] = hooks_data;
    
    std::string filename = FileUtils::JoinPath(m_offsets_directory, FileConstants::COMBINED_FILENAME);
    return WriteJsonFile(filename, combined);
}

std::string FileManager::GenerateTimestampedFilename(const std::string& prefix, const std::string& extension) const {
    std::string timestamp = GetCurrentTimestamp();
    return prefix + "_" + timestamp + extension;
}

bool FileManager::ExportWithTimestamp(const nlohmann::json& data, const std::string& prefix) const {
    if (!m_is_initialized) {
        return false;
    }
    
    std::string filename = GenerateTimestampedFilename(prefix, FileConstants::JSON_EXT);
    std::string full_path = FileUtils::JoinPath(m_offsets_directory, filename);
    return WriteJsonFile(full_path, data);
}

std::string FileManager::GetFullPath(const std::string& relative_path) const {
    if (FileUtils::IsAbsolutePath(relative_path)) {
        return relative_path;
    }
    return FileUtils::JoinPath(m_base_directory, relative_path);
}

bool FileManager::CreateBackup(const std::string& filename) const {
    try {
        std::string source = GetFullPath(filename);
        if (!FileExists(source)) {
            return false;
        }
        
        std::string backup_dir = FileUtils::JoinPath(m_results_directory, FileConstants::BACKUPS_DIR);
        std::string backup_name = FileUtils::GetFilenameWithoutExtension(filename) + "_" + 
                                 GetCurrentTimestamp() + FileConstants::BACKUP_EXT;
        std::string backup_path = FileUtils::JoinPath(backup_dir, backup_name);
        
        return FileUtils::CopyFile(source, backup_path);
    }
    catch (const std::exception&) {
        return false;
    }
}

nlohmann::json FileManager::CreateMetadata() const {
    nlohmann::json metadata;
    metadata["version"] = "1.0.0";
    metadata["generator"] = "FiveM Offset Scanner";
    metadata["timestamp"] = GetCurrentTimestamp();
    metadata["export_config"] = {
        {"export_offsets", m_config.export_offsets},
        {"export_hooks", m_config.export_hooks},
        {"export_patterns", m_config.export_patterns},
        {"export_metadata", m_config.export_metadata},
        {"pretty_print", m_config.pretty_print}
    };
    
    return metadata;
}

bool FileManager::SaveMetadata(const nlohmann::json& metadata) const {
    std::string filename = FileUtils::JoinPath(m_offsets_directory, FileConstants::METADATA_FILENAME);
    return WriteJsonFile(filename, metadata);
}

nlohmann::json FileManager::LoadMetadata() const {
    std::string filename = FileUtils::JoinPath(m_offsets_directory, FileConstants::METADATA_FILENAME);
    return ReadJsonFile(filename);
}

std::string FileManager::GetCurrentTimestamp() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");
    return ss.str();
}

bool FileManager::EnsureDirectoryExists(const std::string& path) const {
    if (!DirectoryExists(path)) {
        return CreateDirectory(path);
    }
    return true;
}

// FileUtils namespace implementation
namespace FileUtils {
    std::string GetExecutableDirectory() {
        try {
            char buffer[MAX_PATH];
            GetModuleFileNameA(nullptr, buffer, MAX_PATH);
            std::string exe_path(buffer);
            return GetDirectoryFromPath(exe_path);
        }
        catch (const std::exception&) {
            return ".";
        }
    }
    
    std::string GetCurrentWorkingDirectory() {
        try {
            return fs::current_path().string();
        }
        catch (const std::exception&) {
            return ".";
        }
    }
    
    std::string JoinPath(const std::string& path1, const std::string& path2) {
        try {
            fs::path p1(path1);
            fs::path p2(path2);
            return (p1 / p2).string();
        }
        catch (const std::exception&) {
            return path1 + "\\" + path2;
        }
    }
    
    std::string GetFileExtension(const std::string& filename) {
        try {
            return fs::path(filename).extension().string();
        }
        catch (const std::exception&) {
            size_t pos = filename.find_last_of('.');
            if (pos != std::string::npos) {
                return filename.substr(pos);
            }
            return "";
        }
    }
    
    std::string GetFilenameWithoutExtension(const std::string& filename) {
        try {
            return fs::path(filename).stem().string();
        }
        catch (const std::exception&) {
            size_t pos = filename.find_last_of('.');
            if (pos != std::string::npos) {
                return filename.substr(0, pos);
            }
            return filename;
        }
    }
    
    std::string GetDirectoryFromPath(const std::string& path) {
        try {
            return fs::path(path).parent_path().string();
        }
        catch (const std::exception&) {
            size_t pos = path.find_last_of("\\/");
            if (pos != std::string::npos) {
                return path.substr(0, pos);
            }
            return ".";
        }
    }
    
    bool IsAbsolutePath(const std::string& path) {
        try {
            return fs::path(path).is_absolute();
        }
        catch (const std::exception&) {
            return path.length() > 1 && path[1] == ':';
        }
    }
    
    std::string NormalizePath(const std::string& path) {
        try {
            return fs::path(path).lexically_normal().string();
        }
        catch (const std::exception&) {
            return path;
        }
    }
    
    bool CopyFile(const std::string& source, const std::string& destination) {
        try {
            return fs::copy_file(source, destination, fs::copy_options::overwrite_existing);
        }
        catch (const std::exception&) {
            return false;
        }
    }
}
