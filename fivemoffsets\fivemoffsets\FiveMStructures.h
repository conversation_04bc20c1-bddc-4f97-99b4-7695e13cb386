#pragma once
#include "framework.h"
#include "PatternScanner.h"

class FiveMStructures {
public:
    struct OffsetInfo {
        std::string name;
        std::string description;
        uintptr_t address;
        uintptr_t rva;
        std::string pattern_used;
        bool is_valid;
        std::string category;
        
        OffsetInfo() : address(0), rva(0), is_valid(false) {}
        OffsetInfo(const std::string& n, const std::string& desc, uintptr_t addr, uintptr_t base, 
                  const std::string& pattern, const std::string& cat)
            : name(n), description(desc), address(addr), rva(addr - base), 
              pattern_used(pattern), is_valid(true), category(cat) {}
    };

private:
    std::unique_ptr<PatternScanner> m_scanner;
    std::map<std::string, OffsetInfo> m_offsets;
    uintptr_t m_base_address;

public:
    FiveMStructures();
    ~FiveMStructures();

    bool Initialize();
    bool ScanAllStructures();
    
    // Getters
    OffsetInfo GetOffset(const std::string& name) const;
    std::map<std::string, OffsetInfo> GetAllOffsets() const { return m_offsets; }
    std::vector<OffsetInfo> GetOffsetsByCategory(const std::string& category) const;
    
    // Export functions
    nlohmann::json ExportToJson() const;
    bool ExportToFile(const std::string& filename) const;

private:
    void InitializePatterns();
    
    // Structure scanning methods
    bool ScanPlayerStructures();
    bool ScanVehicleStructures();
    bool ScanPedStructures();
    bool ScanWorldStructures();
    bool ScanNetworkStructures();
    bool ScanGameStructures();
    
    // Helper methods
    void AddOffset(const std::string& name, const std::string& description, 
                  uintptr_t address, const std::string& pattern, const std::string& category);
    uintptr_t FindPatternWithFallback(const std::vector<std::string>& patterns, const std::string& name);
    
    // Advanced offset resolution
    uintptr_t ResolveIndirectOffset(uintptr_t base_address, const std::vector<int>& offsets);
    uintptr_t FindVTableFunction(uintptr_t vtable_address, int function_index);
};

// Known FiveM patterns and signatures
namespace FiveMPatterns {
    // Player related patterns
    constexpr const char* PLAYER_INFO_ARRAY = "48 8B 05 ? ? ? ? 41 0F BF C8 48 8B 14 C8";
    constexpr const char* LOCAL_PLAYER = "48 8B 05 ? ? ? ? 45 33 C0 48 8B 88 ? ? ? ?";
    constexpr const char* PLAYER_COUNT = "8B 35 ? ? ? ? 85 F6 7E 2A";
    
    // Vehicle related patterns
    constexpr const char* VEHICLE_POOL = "48 8B 05 ? ? ? ? F3 0F 59 F6 48 8B 08";
    constexpr const char* VEHICLE_SPAWN = "48 89 5C 24 ? 48 89 74 24 ? 57 48 83 EC 20 8B FA 8B F1";
    
    // Ped related patterns
    constexpr const char* PED_POOL = "48 8B 05 ? ? ? ? 41 0F BF C8 48 8B 14 C8 48 8B 02";
    constexpr const char* PED_FACTORY = "48 8B 05 ? ? ? ? 48 8B 88 ? ? ? ? 48 85 C9 74 52";
    
    // World related patterns
    constexpr const char* WORLD_PTR = "48 8B 05 ? ? ? ? 45 ? ? ? ? 48 8B 48 08 48 85 C9";
    constexpr const char* BLIP_LIST = "4C 8D 05 ? ? ? ? 0F B7 C1";
    
    // Network related patterns
    constexpr const char* NETWORK_PLAYER_MGR = "48 8B 0D ? ? ? ? 8A D3 48 8B 01 FF 50 ? 4C 8B 07";
    constexpr const char* NETWORK_OBJECT_MGR = "48 8B 0D ? ? ? ? 45 33 C0 E8 ? ? ? ? 33 FF 4C 8B F0";
    
    // Game state patterns
    constexpr const char* GAME_STATE = "83 3D ? ? ? ? ? 75 17 8B 43 20 25";
    constexpr const char* FRAME_COUNT = "8B 15 ? ? ? ? 41 FF C1";
    
    // Memory pools
    constexpr const char* ENTITY_POOL = "4C 8B 0D ? ? ? ? 44 8B C1 49 8B 41 08";
    constexpr const char* PICKUP_POOL = "48 8B 05 ? ? ? ? 8B 78 10 85 FF";
    
    // Camera patterns
    constexpr const char* CAMERA_MANAGER = "48 8B 05 ? ? ? ? 48 8B 88 ? ? ? ? 48 85 C9 74 06";
    constexpr const char* VIEWPORT_MANAGER = "48 8B 15 ? ? ? ? 48 8D 2D ? ? ? ?";
    
    // Script patterns
    constexpr const char* SCRIPT_THREAD = "45 33 F6 8B E9 85 C9 0F 8E ? ? ? ? 44 8B 15";
    constexpr const char* GLOBAL_TABLE = "48 8D 15 ? ? ? ? 4C 8B C0 E8 ? ? ? ? 48 85 C0 75 0A";
    
    // Weapon patterns
    constexpr const char* WEAPON_MANAGER = "48 8B 05 ? ? ? ? 8B 88 ? ? ? ? 85 C9 74 09";
    constexpr const char* WEAPON_INFO = "45 33 C9 4C 8B DA 85 D2 0F 8E ? ? ? ?";
    
    // Audio patterns
    constexpr const char* AUDIO_ENGINE = "48 8B 0D ? ? ? ? 33 D2 C6 44 24 ? ? E8 ? ? ? ?";
    
    // Graphics patterns
    constexpr const char* GRAPHICS_DEVICE = "48 8B 0D ? ? ? ? 48 8B 01 44 8D 43 01 33 D2";
    constexpr const char* RENDER_THREAD = "48 8B 0D ? ? ? ? BA ? ? ? ? E8 ? ? ? ? 48 8B D8";
    
    // Input patterns
    constexpr const char* INPUT_MANAGER = "48 8B 0D ? ? ? ? 8B D3 E8 ? ? ? ? 84 C0 75 08";
    constexpr const char* CONTROL_MANAGER = "40 53 48 83 EC 20 8B D9 E8 ? ? ? ? 84 C0 75 13";
}
