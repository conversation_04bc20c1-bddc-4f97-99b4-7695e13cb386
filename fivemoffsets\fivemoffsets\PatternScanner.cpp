#include "pch.h"
#include "PatternScanner.h"

PatternScanner::Pattern::Pattern(const std::string& pattern, const std::string& name, const std::string& desc)
    : name(name), description(desc) {
    // Create a temporary scanner instance to access the methods
    PatternScanner temp_scanner;
    bytes = temp_scanner.PatternStringToBytes(pattern);
    mask = temp_scanner.PatternStringToMask(pattern);
}

PatternScanner::PatternScanner(const std::string& module_name)
    : m_base_address(0), m_module_size(0), m_module_name(module_name) {
    if (!module_name.empty()) {
        Initialize(module_name);
    }
}

PatternScanner::~PatternScanner() {
    ClearPatterns();
}

bool PatternScanner::Initialize(const std::string& module_name) {
    HMODULE hModule = nullptr;
    
    if (module_name.empty() || module_name == "main" || module_name == "exe") {
        hModule = GetModuleHandle(nullptr);
        m_module_name = "main";
    } else {
        hModule = GetModuleHandleA(module_name.c_str());
        m_module_name = module_name;
    }
    
    if (!hModule) {
        return false;
    }
    
    m_base_address = reinterpret_cast<uintptr_t>(hModule);
    
    // Get module size
    MODULEINFO modInfo;
    if (GetModuleInformation(GetCurrentProcess(), hModule, &modInfo, sizeof(modInfo))) {
        m_module_size = modInfo.SizeOfImage;
        return true;
    }
    
    return false;
}

void PatternScanner::AddPattern(const std::string& pattern, const std::string& name, const std::string& description) {
    m_patterns.emplace_back(pattern, name, description);
}

void PatternScanner::AddPattern(const Pattern& pattern) {
    m_patterns.push_back(pattern);
}

void PatternScanner::ClearPatterns() {
    m_patterns.clear();
    m_results.clear();
}

uintptr_t PatternScanner::ScanPattern(const std::string& pattern, size_t start_offset) {
    Pattern p(pattern);
    return ScanPattern(p, start_offset);
}

uintptr_t PatternScanner::ScanPattern(const Pattern& pattern, size_t start_offset) {
    if (!IsInitialized() || pattern.bytes.empty()) {
        return 0;
    }
    
    const uint8_t* scan_start = reinterpret_cast<const uint8_t*>(m_base_address + start_offset);
    const uint8_t* scan_end = reinterpret_cast<const uint8_t*>(m_base_address + m_module_size - pattern.bytes.size());
    
    for (const uint8_t* current = scan_start; current <= scan_end; ++current) {
        if (ComparePattern(current, pattern)) {
            return reinterpret_cast<uintptr_t>(current);
        }
    }
    
    return 0;
}

std::vector<uintptr_t> PatternScanner::ScanPatternMultiple(const std::string& pattern, size_t max_results) {
    std::vector<uintptr_t> results;
    Pattern p(pattern);
    
    if (!IsInitialized() || p.bytes.empty()) {
        return results;
    }
    
    const uint8_t* scan_start = reinterpret_cast<const uint8_t*>(m_base_address);
    const uint8_t* scan_end = reinterpret_cast<const uint8_t*>(m_base_address + m_module_size - p.bytes.size());
    
    for (const uint8_t* current = scan_start; current <= scan_end && results.size() < max_results; ++current) {
        if (ComparePattern(current, p)) {
            results.push_back(reinterpret_cast<uintptr_t>(current));
            current += p.bytes.size() - 1; // Skip ahead to avoid overlapping matches
        }
    }
    
    return results;
}

bool PatternScanner::ScanAllPatterns() {
    if (!IsInitialized()) {
        return false;
    }
    
    m_results.clear();
    
    for (const auto& pattern : m_patterns) {
        uintptr_t result = ScanPattern(pattern);
        if (result != 0) {
            m_results[pattern.name] = ScanResult(result, pattern.name, pattern.description, m_base_address);
        }
    }
    
    return !m_results.empty();
}

PatternScanner::ScanResult PatternScanner::GetResult(const std::string& pattern_name) const {
    auto it = m_results.find(pattern_name);
    if (it != m_results.end()) {
        return it->second;
    }
    return ScanResult();
}

std::vector<uint8_t> PatternScanner::ReadBytes(uintptr_t address, size_t size) const {
    std::vector<uint8_t> bytes;
    
    if (!IsAddressReadable(address, size)) {
        return bytes;
    }
    
    bytes.resize(size);
    __try {
        memcpy(bytes.data(), reinterpret_cast<void*>(address), size);
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        bytes.clear();
    }
    
    return bytes;
}

uintptr_t PatternScanner::FollowCall(uintptr_t call_address) const {
    if (!IsAddressReadable(call_address, 5)) {
        return 0;
    }
    
    // Check if it's a call instruction (0xE8)
    uint8_t opcode = ReadMemory<uint8_t>(call_address);
    if (opcode != 0xE8) {
        return 0;
    }
    
    // Read the relative offset
    int32_t offset = ReadMemory<int32_t>(call_address + 1);
    
    // Calculate the target address
    return call_address + 5 + offset;
}

uintptr_t PatternScanner::FollowJump(uintptr_t jump_address) const {
    if (!IsAddressReadable(jump_address, 5)) {
        return 0;
    }
    
    // Check if it's a jump instruction (0xE9)
    uint8_t opcode = ReadMemory<uint8_t>(jump_address);
    if (opcode != 0xE9) {
        return 0;
    }
    
    // Read the relative offset
    int32_t offset = ReadMemory<int32_t>(jump_address + 1);
    
    // Calculate the target address
    return jump_address + 5 + offset;
}

uintptr_t PatternScanner::ResolveRelativeAddress(uintptr_t instruction_address, int32_t offset, size_t instruction_size) const {
    return instruction_address + instruction_size + offset;
}

bool PatternScanner::ComparePattern(const uint8_t* data, const Pattern& pattern) const {
    for (size_t i = 0; i < pattern.bytes.size(); ++i) {
        if (pattern.mask[i] && data[i] != pattern.bytes[i]) {
            return false;
        }
    }
    return true;
}

std::vector<uint8_t> PatternScanner::PatternStringToBytes(const std::string& pattern) const {
    std::vector<uint8_t> bytes;
    std::istringstream iss(pattern);
    std::string byte_str;
    
    while (iss >> byte_str) {
        if (byte_str == "?" || byte_str == "??") {
            bytes.push_back(0x00); // Wildcard byte (value doesn't matter)
        } else {
            bytes.push_back(static_cast<uint8_t>(std::stoul(byte_str, nullptr, 16)));
        }
    }
    
    return bytes;
}

std::vector<bool> PatternScanner::PatternStringToMask(const std::string& pattern) const {
    std::vector<bool> mask;
    std::istringstream iss(pattern);
    std::string byte_str;
    
    while (iss >> byte_str) {
        mask.push_back(byte_str != "?" && byte_str != "??");
    }
    
    return mask;
}

bool PatternScanner::IsAddressValid(uintptr_t address) const {
    return address >= m_base_address && address < (m_base_address + m_module_size);
}

bool PatternScanner::IsAddressReadable(uintptr_t address, size_t size) const {
    if (!IsAddressValid(address) || !IsAddressValid(address + size - 1)) {
        return false;
    }
    
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi)) == 0) {
        return false;
    }
    
    return (mbi.State == MEM_COMMIT) && 
           (mbi.Protect & (PAGE_READONLY | PAGE_READWRITE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE));
}
