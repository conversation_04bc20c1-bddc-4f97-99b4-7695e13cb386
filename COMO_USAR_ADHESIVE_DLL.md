# 🎯 Como Usar a DLL com Truque do adhesive.dll

## 📋 **Método 1: DLL Hijacking com adhesive.dll (RECOMENDADO)**

### **Passo 1: Localizar o adhesive.dll**
```
C:\Windows\System32\adhesive.dll
```

### **Passo 2: Backup e Renomeação**
1. **Copie** `adhesive.dll` para `adhesive_original.dll`
2. **Renomeie** nossa DLL compilada para `adhesive.dll`
3. **Coloque** na pasta do FiveM ou System32

### **Passo 3: Execução Automática**
1. **Execute** o FiveM normalmente
2. **A DLL será carregada automaticamente**
3. **Aguarde** a mensagem de sucesso
4. **Verifique** a pasta `results/offsets/`

---

## 📋 **Método 2: Injeção Manual (ALTERNATIVO)**

### **Usando Process Hacker:**
1. Abra o **Process Hacker** como administrador
2. Encontre o processo **FiveM.exe** ou **GTA5.exe**
3. Clique com botão direito → **Miscellaneous** → **Inject DLL**
4. Selecione nossa DLL compilada
5. Aguarde a execução automática

### **Usando Cheat Engine:**
1. Abra o **Cheat Engine** como administrador
2. Selecione o processo **FiveM.exe**
3. **Memory View** → **Tools** → **Inject DLL**
4. Selecione nossa DLL
5. Aguarde a execução automática

---

## 🎯 **Como Funciona o Truque**

### **DLL Hijacking Explicado:**
```
1. FiveM carrega automaticamente certas DLLs do sistema
2. adhesive.dll é uma DLL legítima que pode ser "sequestrada"
3. Colocamos nossa DLL no lugar da original
4. FiveM carrega nossa DLL pensando que é a original
5. Nossa DLL executa o scan automaticamente
```

### **Vantagens:**
- ✅ **Execução automática** sem intervenção manual
- ✅ **Não precisa estar em servidor**
- ✅ **Funciona no menu principal** do FiveM
- ✅ **Detecção automática** do processo FiveM
- ✅ **Export automático** dos resultados

---

## 📁 **Resultados Exportados**

Após a execução, verifique a pasta:
```
results/offsets/
├── offsets.json          # Offsets em formato JSON
├── offsets.ct            # Tabela do Cheat Engine
├── offsets.h             # Header C++ com defines
├── hooks.json            # Informações de hooks
└── metadata.json         # Metadados do scan
```

---

## 🔧 **Solução de Problemas**

### **DLL não carrega:**
- Verifique se está como **administrador**
- Confirme se o **adhesive.dll original** foi renomeado
- Teste com **injeção manual** primeiro

### **Scan falha:**
- Aguarde o **FiveM carregar completamente**
- Verifique se está no **processo correto** (GTA5.exe)
- Teste em **modo single-player** primeiro

### **Sem resultados:**
- Verifique **permissões de escrita** na pasta
- Confirme se a pasta `results/offsets/` foi criada
- Verifique o **DebugView** para logs detalhados

---

## 🚀 **Dicas Avançadas**

### **Para Desenvolvedores:**
- Use **DebugView** para monitorar logs em tempo real
- A DLL detecta automaticamente se está no FiveM
- Aguarda os módulos necessários carregarem
- Exporta em múltiplos formatos para máxima compatibilidade

### **Para Usuários:**
- **Não precisa estar em servidor** para funcionar
- Funciona no **menu principal** do FiveM
- **Aguarde a mensagem** de conclusão
- Os offsets são **atualizados automaticamente**

---

## ⚠️ **Avisos Importantes**

1. **Sempre faça backup** do adhesive.dll original
2. **Use apenas para fins educacionais** e desenvolvimento
3. **Respeite os termos de serviço** do FiveM
4. **Teste em ambiente controlado** primeiro
