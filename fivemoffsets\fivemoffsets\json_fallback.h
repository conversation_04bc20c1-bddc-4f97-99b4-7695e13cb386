#pragma once
#include <string>
#include <map>
#include <vector>
#include <sstream>
#include <iomanip>

// Simple JSON fallback implementation
// This is a minimal JSON implementation to avoid dependency issues
namespace nlohmann {
    class json {
    public:
        enum value_t {
            null,
            object,
            array,
            string,
            boolean,
            number_integer,
            number_unsigned,
            number_float
        };

        // Type aliases for compatibility
        using array_t = std::vector<json>;
        using object_t = std::map<std::string, json>;

    private:
        value_t m_type = null;
        std::string m_string_value;
        std::map<std::string, json> m_object_value;
        std::vector<json> m_array_value;
        int64_t m_int_value = 0;
        uint64_t m_uint_value = 0;
        double m_float_value = 0.0;
        bool m_bool_value = false;

    public:
        // Constructors
        json() : m_type(null) {}
        json(std::nullptr_t) : m_type(null) {}
        json(const std::string& value) : m_type(string), m_string_value(value) {}
        json(const char* value) : m_type(string), m_string_value(value) {}
        json(bool value) : m_type(boolean), m_bool_value(value) {}
        json(int value) : m_type(number_integer), m_int_value(value) {}
        json(int64_t value) : m_type(number_integer), m_int_value(value) {}
        json(uint64_t value) : m_type(number_unsigned), m_uint_value(value) {}
        json(double value) : m_type(number_float), m_float_value(value) {}

        // Object constructor
        json(const std::map<std::string, json>& obj) : m_type(object), m_object_value(obj) {}
        
        // Array constructor
        json(const std::vector<json>& arr) : m_type(array), m_array_value(arr) {}

        // Assignment operators
        json& operator=(const std::string& value) {
            m_type = string;
            m_string_value = value;
            return *this;
        }

        json& operator=(bool value) {
            m_type = boolean;
            m_bool_value = value;
            return *this;
        }

        json& operator=(int value) {
            m_type = number_integer;
            m_int_value = value;
            return *this;
        }

        json& operator=(int64_t value) {
            m_type = number_integer;
            m_int_value = value;
            return *this;
        }

        json& operator=(uint64_t value) {
            m_type = number_unsigned;
            m_uint_value = value;
            return *this;
        }

        json& operator=(double value) {
            m_type = number_float;
            m_float_value = value;
            return *this;
        }

        // Object access
        json& operator[](const std::string& key) {
            if (m_type != object) {
                m_type = object;
                m_object_value.clear();
            }
            return m_object_value[key];
        }

        const json& operator[](const std::string& key) const {
            static json null_json;
            if (m_type != object) return null_json;
            auto it = m_object_value.find(key);
            return (it != m_object_value.end()) ? it->second : null_json;
        }

        // Array access
        json& operator[](size_t index) {
            if (m_type != array) {
                m_type = array;
                m_array_value.clear();
            }
            if (index >= m_array_value.size()) {
                m_array_value.resize(index + 1);
            }
            return m_array_value[index];
        }

        // Type checking
        bool is_null() const { return m_type == null; }
        bool is_object() const { return m_type == object; }
        bool is_array() const { return m_type == array; }
        bool is_string() const { return m_type == string; }
        bool is_boolean() const { return m_type == boolean; }
        bool is_number() const { return m_type == number_integer || m_type == number_unsigned || m_type == number_float; }

        // Value getters
        std::string get_string() const {
            if (m_type == string) return m_string_value;
            return "";
        }

        bool get_bool() const {
            if (m_type == boolean) return m_bool_value;
            return false;
        }

        int64_t get_int() const {
            if (m_type == number_integer) return m_int_value;
            if (m_type == number_unsigned) return static_cast<int64_t>(m_uint_value);
            if (m_type == number_float) return static_cast<int64_t>(m_float_value);
            return 0;
        }

        uint64_t get_uint() const {
            if (m_type == number_unsigned) return m_uint_value;
            if (m_type == number_integer) return static_cast<uint64_t>(m_int_value);
            if (m_type == number_float) return static_cast<uint64_t>(m_float_value);
            return 0;
        }

        double get_double() const {
            if (m_type == number_float) return m_float_value;
            if (m_type == number_integer) return static_cast<double>(m_int_value);
            if (m_type == number_unsigned) return static_cast<double>(m_uint_value);
            return 0.0;
        }

        // Size
        size_t size() const {
            if (m_type == object) return m_object_value.size();
            if (m_type == array) return m_array_value.size();
            return 0;
        }

        // Clear
        void clear() {
            m_type = null;
            m_string_value.clear();
            m_object_value.clear();
            m_array_value.clear();
            m_int_value = 0;
            m_uint_value = 0;
            m_float_value = 0.0;
            m_bool_value = false;
        }

        // Dump to string
        std::string dump(int indent = -1) const {
            return dump_impl(0, indent);
        }

    private:
        std::string dump_impl(int current_indent, int indent_size) const {
            std::stringstream ss;
            
            switch (m_type) {
                case null:
                    ss << "null";
                    break;
                case boolean:
                    ss << (m_bool_value ? "true" : "false");
                    break;
                case number_integer:
                    ss << m_int_value;
                    break;
                case number_unsigned:
                    ss << m_uint_value;
                    break;
                case number_float:
                    ss << std::fixed << std::setprecision(6) << m_float_value;
                    break;
                case string:
                    ss << "\"" << escape_string(m_string_value) << "\"";
                    break;
                case object:
                {
                    ss << "{";
                    if (indent_size > 0) ss << "\n";

                    bool first = true;
                    for (const auto& pair : m_object_value) {
                        if (!first) {
                            ss << ",";
                            if (indent_size > 0) ss << "\n";
                        }
                        first = false;

                        if (indent_size > 0) {
                            ss << std::string(current_indent + indent_size, ' ');
                        }

                        ss << "\"" << escape_string(pair.first) << "\":";
                        if (indent_size > 0) ss << " ";
                        ss << pair.second.dump_impl(current_indent + indent_size, indent_size);
                    }

                    if (indent_size > 0 && !m_object_value.empty()) {
                        ss << "\n" << std::string(current_indent, ' ');
                    }
                    ss << "}";
                    break;
                }
                case array:
                {
                    ss << "[";
                    if (indent_size > 0) ss << "\n";

                    for (size_t i = 0; i < m_array_value.size(); ++i) {
                        if (i > 0) {
                            ss << ",";
                            if (indent_size > 0) ss << "\n";
                        }

                        if (indent_size > 0) {
                            ss << std::string(current_indent + indent_size, ' ');
                        }

                        ss << m_array_value[i].dump_impl(current_indent + indent_size, indent_size);
                    }

                    if (indent_size > 0 && !m_array_value.empty()) {
                        ss << "\n" << std::string(current_indent, ' ');
                    }
                    ss << "]";
                    break;
                }
            }
            
            return ss.str();
        }

        std::string escape_string(const std::string& str) const {
            std::string result;
            for (char c : str) {
                switch (c) {
                    case '"': result += "\\\""; break;
                    case '\\': result += "\\\\"; break;
                    case '\b': result += "\\b"; break;
                    case '\f': result += "\\f"; break;
                    case '\n': result += "\\n"; break;
                    case '\r': result += "\\r"; break;
                    case '\t': result += "\\t"; break;
                    default: result += c; break;
                }
            }
            return result;
        }
    };
}
