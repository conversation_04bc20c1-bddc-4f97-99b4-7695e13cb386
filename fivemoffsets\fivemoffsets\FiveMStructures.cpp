#include "pch.h"
#include "FiveMStructures.h"

FiveMStructures::FiveMStructures() : m_base_address(0) {
    m_scanner = std::make_unique<PatternScanner>();
}

FiveMStructures::~FiveMStructures() = default;

bool FiveMStructures::Initialize() {
    if (!m_scanner->Initialize()) {
        return false;
    }
    
    m_base_address = m_scanner->GetBaseAddress();
    InitializePatterns();
    return true;
}

void FiveMStructures::InitializePatterns() {
    // Player patterns
    m_scanner->AddPattern(FiveMPatterns::PLAYER_INFO_ARRAY, "PlayerInfoArray", "Array of player information structures");
    m_scanner->AddPattern(FiveMPatterns::LOCAL_PLAYER, "LocalPlayer", "Local player pointer");
    m_scanner->AddPattern(FiveMPatterns::PLAYER_COUNT, "PlayerCount", "Current player count");
    
    // Vehicle patterns
    m_scanner->AddPattern(FiveMPatterns::VEHICLE_POOL, "VehiclePool", "Vehicle object pool");
    m_scanner->AddPattern(FiveMPatterns::VEHICLE_SPAWN, "VehicleSpawn", "Vehicle spawn function");
    
    // Ped patterns
    m_scanner->AddPattern(FiveMPatterns::PED_POOL, "PedPool", "Ped object pool");
    m_scanner->AddPattern(FiveMPatterns::PED_FACTORY, "PedFactory", "Ped factory instance");
    
    // World patterns
    m_scanner->AddPattern(FiveMPatterns::WORLD_PTR, "WorldPtr", "World pointer");
    m_scanner->AddPattern(FiveMPatterns::BLIP_LIST, "BlipList", "Blip list array");
    
    // Network patterns
    m_scanner->AddPattern(FiveMPatterns::NETWORK_PLAYER_MGR, "NetworkPlayerMgr", "Network player manager");
    m_scanner->AddPattern(FiveMPatterns::NETWORK_OBJECT_MGR, "NetworkObjectMgr", "Network object manager");
    
    // Game state patterns
    m_scanner->AddPattern(FiveMPatterns::GAME_STATE, "GameState", "Current game state");
    m_scanner->AddPattern(FiveMPatterns::FRAME_COUNT, "FrameCount", "Frame counter");
    
    // Memory pools
    m_scanner->AddPattern(FiveMPatterns::ENTITY_POOL, "EntityPool", "Entity pool manager");
    m_scanner->AddPattern(FiveMPatterns::PICKUP_POOL, "PickupPool", "Pickup object pool");
    
    // Camera patterns
    m_scanner->AddPattern(FiveMPatterns::CAMERA_MANAGER, "CameraManager", "Camera manager instance");
    m_scanner->AddPattern(FiveMPatterns::VIEWPORT_MANAGER, "ViewportManager", "Viewport manager");
    
    // Script patterns
    m_scanner->AddPattern(FiveMPatterns::SCRIPT_THREAD, "ScriptThread", "Script thread manager");
    m_scanner->AddPattern(FiveMPatterns::GLOBAL_TABLE, "GlobalTable", "Global variable table");
    
    // Weapon patterns
    m_scanner->AddPattern(FiveMPatterns::WEAPON_MANAGER, "WeaponManager", "Weapon manager instance");
    m_scanner->AddPattern(FiveMPatterns::WEAPON_INFO, "WeaponInfo", "Weapon information array");
    
    // Audio patterns
    m_scanner->AddPattern(FiveMPatterns::AUDIO_ENGINE, "AudioEngine", "Audio engine instance");
    
    // Graphics patterns
    m_scanner->AddPattern(FiveMPatterns::GRAPHICS_DEVICE, "GraphicsDevice", "Graphics device");
    m_scanner->AddPattern(FiveMPatterns::RENDER_THREAD, "RenderThread", "Render thread manager");
    
    // Input patterns
    m_scanner->AddPattern(FiveMPatterns::INPUT_MANAGER, "InputManager", "Input manager");
    m_scanner->AddPattern(FiveMPatterns::CONTROL_MANAGER, "ControlManager", "Control manager");
}

bool FiveMStructures::ScanAllStructures() {
    if (!m_scanner->IsInitialized()) {
        return false;
    }
    
    m_offsets.clear();
    
    // Scan all patterns
    if (!m_scanner->ScanAllPatterns()) {
        return false;
    }
    
    // Process results and resolve addresses
    auto results = m_scanner->GetAllResults();
    for (const auto& [name, result] : results) {
        if (result.is_valid) {
            // For patterns that contain relative addresses, resolve them
            uintptr_t final_address = result.address;
            
            // Check if this pattern contains a relative address that needs to be resolved
            if (name.find("Array") != std::string::npos || 
                name.find("Pool") != std::string::npos ||
                name.find("Mgr") != std::string::npos ||
                name.find("Manager") != std::string::npos ||
                name.find("Ptr") != std::string::npos) {
                
                // Most patterns use LEA or MOV with RIP-relative addressing
                // Pattern: 48 8B 05 ? ? ? ? (MOV RAX, [RIP+offset])
                // Pattern: 4C 8D 05 ? ? ? ? (LEA R8, [RIP+offset])
                
                uint8_t opcode1 = m_scanner->ReadMemory<uint8_t>(final_address);
                uint8_t opcode2 = m_scanner->ReadMemory<uint8_t>(final_address + 1);
                uint8_t opcode3 = m_scanner->ReadMemory<uint8_t>(final_address + 2);
                
                if ((opcode1 == 0x48 && opcode2 == 0x8B && opcode3 == 0x05) ||  // MOV RAX, [RIP+offset]
                    (opcode1 == 0x4C && opcode2 == 0x8D && opcode3 == 0x05) ||  // LEA R8, [RIP+offset]
                    (opcode1 == 0x48 && opcode2 == 0x8D && opcode3 == 0x15)) {  // LEA RDX, [RIP+offset]
                    
                    int32_t offset = m_scanner->ReadMemory<int32_t>(final_address + 3);
                    final_address = m_scanner->ResolveRelativeAddress(final_address, offset, 7);
                    
                    // For pointers, we might need to dereference once more
                    if (name.find("Ptr") != std::string::npos) {
                        uintptr_t deref_addr = m_scanner->ReadMemory<uintptr_t>(final_address);
                        if (deref_addr != 0) {
                            final_address = deref_addr;
                        }
                    }
                }
            }
            
            AddOffset(name, result.description, final_address, result.pattern_name, "Core");
        }
    }
    
    // Perform additional structure-specific scanning
    ScanPlayerStructures();
    ScanVehicleStructures();
    ScanPedStructures();
    ScanWorldStructures();
    ScanNetworkStructures();
    ScanGameStructures();
    
    return !m_offsets.empty();
}

bool FiveMStructures::ScanPlayerStructures() {
    // Additional player-related offsets that require special handling
    auto player_info_result = m_scanner->GetResult("PlayerInfoArray");
    if (player_info_result.is_valid) {
        // Try to find player info structure offsets
        uintptr_t player_info_base = player_info_result.address;
        
        // Common player info offsets (these may need adjustment based on game version)
        AddOffset("PlayerInfo.Name", "Player name offset", player_info_base + 0x10, "Manual", "Player");
        AddOffset("PlayerInfo.Ped", "Player ped pointer", player_info_base + 0x28, "Manual", "Player");
        AddOffset("PlayerInfo.Vehicle", "Player vehicle pointer", player_info_base + 0x30, "Manual", "Player");
        AddOffset("PlayerInfo.WantedLevel", "Player wanted level", player_info_base + 0x888, "Manual", "Player");
    }
    
    return true;
}

bool FiveMStructures::ScanVehicleStructures() {
    // Vehicle-specific structure scanning
    auto vehicle_pool_result = m_scanner->GetResult("VehiclePool");
    if (vehicle_pool_result.is_valid) {
        // Vehicle structure offsets
        AddOffset("Vehicle.Health", "Vehicle health", 0x280, "Manual", "Vehicle");
        AddOffset("Vehicle.Position", "Vehicle position", 0x90, "Manual", "Vehicle");
        AddOffset("Vehicle.Rotation", "Vehicle rotation", 0xA0, "Manual", "Vehicle");
        AddOffset("Vehicle.Velocity", "Vehicle velocity", 0x70, "Manual", "Vehicle");
        AddOffset("Vehicle.Model", "Vehicle model hash", 0x18, "Manual", "Vehicle");
    }
    
    return true;
}

bool FiveMStructures::ScanPedStructures() {
    // Ped-specific structure scanning
    auto ped_pool_result = m_scanner->GetResult("PedPool");
    if (ped_pool_result.is_valid) {
        // Ped structure offsets
        AddOffset("Ped.Health", "Ped health", 0x280, "Manual", "Ped");
        AddOffset("Ped.MaxHealth", "Ped max health", 0x2A0, "Manual", "Ped");
        AddOffset("Ped.Position", "Ped position", 0x90, "Manual", "Ped");
        AddOffset("Ped.Rotation", "Ped rotation", 0xA0, "Manual", "Ped");
        AddOffset("Ped.Velocity", "Ped velocity", 0x70, "Manual", "Ped");
        AddOffset("Ped.WeaponManager", "Ped weapon manager", 0x10B8, "Manual", "Ped");
    }
    
    return true;
}

bool FiveMStructures::ScanWorldStructures() {
    // World-specific structure scanning
    return true;
}

bool FiveMStructures::ScanNetworkStructures() {
    // Network-specific structure scanning
    return true;
}

bool FiveMStructures::ScanGameStructures() {
    // Game-specific structure scanning
    return true;
}

void FiveMStructures::AddOffset(const std::string& name, const std::string& description, 
                               uintptr_t address, const std::string& pattern, const std::string& category) {
    m_offsets[name] = OffsetInfo(name, description, address, m_base_address, pattern, category);
}

FiveMStructures::OffsetInfo FiveMStructures::GetOffset(const std::string& name) const {
    auto it = m_offsets.find(name);
    if (it != m_offsets.end()) {
        return it->second;
    }
    return OffsetInfo();
}

std::vector<FiveMStructures::OffsetInfo> FiveMStructures::GetOffsetsByCategory(const std::string& category) const {
    std::vector<OffsetInfo> result;
    for (const auto& [name, offset] : m_offsets) {
        if (offset.category == category) {
            result.push_back(offset);
        }
    }
    return result;
}

nlohmann::json FiveMStructures::ExportToJson() const {
    nlohmann::json j;
    j["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    j["base_address"] = m_base_address;
    j["module_name"] = m_scanner->GetModuleName();
    j["module_size"] = m_scanner->GetModuleSize();
    
    nlohmann::json offsets_json;
    for (const auto& [name, offset] : m_offsets) {
        nlohmann::json offset_json;
        offset_json["name"] = offset.name;
        offset_json["description"] = offset.description;
        offset_json["address"] = offset.address;
        offset_json["rva"] = offset.rva;
        offset_json["pattern_used"] = offset.pattern_used;
        offset_json["category"] = offset.category;
        offset_json["is_valid"] = offset.is_valid;
        
        offsets_json[name] = offset_json;
    }
    
    j["offsets"] = offsets_json;
    return j;
}

bool FiveMStructures::ExportToFile(const std::string& filename) const {
    try {
        nlohmann::json j = ExportToJson();
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }
        
        file << j.dump(4); // Pretty print with 4 spaces
        file.close();
        return true;
    }
    catch (const std::exception&) {
        return false;
    }
}
