#pragma once

#define WIN32_LEAN_AND_MEAN             // Excluir itens raramente utilizados dos cabeçalhos do Windows
// Arquivos de Cabeçalho do Windows
#include <windows.h>
#include <psapi.h>
#include <tlhelp32.h>
#include <vector>
#include <string>
#include <memory>
#include <fstream>
#include <iostream>
#include <iomanip>
#include <sstream>
#include <map>
#include <algorithm>
#include <thread>
#include <chrono>

// JSON library (nlohmann/json)
#include <nlohmann/json.hpp>

// Detours library for hooking
#include <detours.h>

#pragma comment(lib, "psapi.lib")
#pragma comment(lib, "detours.lib")
