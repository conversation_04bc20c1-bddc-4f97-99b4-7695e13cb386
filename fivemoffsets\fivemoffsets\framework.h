#pragma once

#define WIN32_LEAN_AND_MEAN             // Excluir itens raramente utilizados dos cabeçalhos do Windows
// Arquivos de Cabeçalho do Windows
#include <windows.h>
#include <psapi.h>
#include <tlhelp32.h>
#include <vector>
#include <string>
#include <memory>
#include <fstream>
#include <iostream>
#include <iomanip>
#include <sstream>
#include <map>
#include <algorithm>
#include <thread>
#include <chrono>
#include <filesystem>
#include <functional>
#include <mutex>
#include <atomic>
#include <ctime>
#include <random>
#include <regex>
#include <unordered_set>
#include <unordered_map>

// JSON library - will be included conditionally
#ifdef NLOHMANN_JSON_AVAILABLE
#include <nlohmann/json.hpp>
#else
// Fallback JSON implementation
#include "json_fallback.h"
#endif

// Microsoft Detours for hooking - will be included conditionally
#ifdef DETOURS_AVAILABLE
#include <detours.h>
#else
// Fallback detours definitions
#include "detours_fallback.h"
#endif

#pragma comment(lib, "psapi.lib")
// Note: detours.lib is not linked when using fallback implementation
