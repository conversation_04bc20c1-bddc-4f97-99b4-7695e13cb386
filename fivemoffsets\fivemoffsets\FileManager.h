#pragma once
#include "framework.h"

class FileManager {
public:
    struct ExportConfig {
        bool export_offsets;
        bool export_hooks;
        bool export_patterns;
        bool export_metadata;
        bool pretty_print;
        std::string output_directory;
        std::string filename_prefix;
        
        ExportConfig() : export_offsets(true), export_hooks(true), export_patterns(true),
                        export_metadata(true), pretty_print(true), 
                        output_directory("results/offsets"), filename_prefix("fivem_offsets") {}
    };

private:
    std::string m_base_directory;
    std::string m_results_directory;
    std::string m_offsets_directory;
    ExportConfig m_config;
    bool m_is_initialized;

public:
    FileManager();
    ~FileManager();

    // Initialization
    bool Initialize(const std::string& base_dir = "");
    bool IsInitialized() const { return m_is_initialized; }
    
    // Directory management
    bool CreateDirectories();
    bool DirectoryExists(const std::string& path) const;
    bool CreateDirectory(const std::string& path) const;
    
    // File operations
    bool WriteFile(const std::string& filename, const std::string& content) const;
    bool WriteJsonFile(const std::string& filename, const nlohmann::json& json) const;
    std::string ReadFile(const std::string& filename) const;
    nlohmann::json ReadJsonFile(const std::string& filename) const;
    
    bool FileExists(const std::string& filename) const;
    bool DeleteFile(const std::string& filename) const;
    std::vector<std::string> ListFiles(const std::string& directory, const std::string& extension = "") const;
    
    // Export functions
    bool ExportOffsets(const nlohmann::json& offsets_data) const;
    bool ExportHooks(const nlohmann::json& hooks_data) const;
    bool ExportCombined(const nlohmann::json& offsets_data, const nlohmann::json& hooks_data) const;
    
    // Automatic export with timestamp
    std::string GenerateTimestampedFilename(const std::string& prefix, const std::string& extension = ".json") const;
    bool ExportWithTimestamp(const nlohmann::json& data, const std::string& prefix) const;
    
    // Configuration
    void SetExportConfig(const ExportConfig& config) { m_config = config; }
    ExportConfig GetExportConfig() const { return m_config; }
    
    // Path utilities
    std::string GetResultsDirectory() const { return m_results_directory; }
    std::string GetOffsetsDirectory() const { return m_offsets_directory; }
    std::string GetFullPath(const std::string& relative_path) const;
    
    // Backup and versioning
    bool CreateBackup(const std::string& filename) const;
    bool RestoreBackup(const std::string& filename) const;
    std::vector<std::string> ListBackups() const;
    bool CleanOldBackups(int max_backups = 10) const;
    
    // Metadata management
    nlohmann::json CreateMetadata() const;
    bool SaveMetadata(const nlohmann::json& metadata) const;
    nlohmann::json LoadMetadata() const;
    
    // Template export functions
    bool ExportCheatEngineTable(const nlohmann::json& offsets_data) const;
    bool ExportCppHeader(const nlohmann::json& offsets_data) const;
    bool ExportPythonScript(const nlohmann::json& offsets_data) const;
    bool ExportLuaScript(const nlohmann::json& offsets_data) const;

private:
    std::string GetCurrentTimestamp() const;
    std::string SanitizeFilename(const std::string& filename) const;
    bool EnsureDirectoryExists(const std::string& path) const;
    
    // Template generators
    std::string GenerateCheatEngineXML(const nlohmann::json& offsets_data) const;
    std::string GenerateCppHeaderContent(const nlohmann::json& offsets_data) const;
    std::string GeneratePythonScriptContent(const nlohmann::json& offsets_data) const;
    std::string GenerateLuaScriptContent(const nlohmann::json& offsets_data) const;
};

// Utility functions for file operations
namespace FileUtils {
    std::string GetExecutableDirectory();
    std::string GetCurrentWorkingDirectory();
    std::string JoinPath(const std::string& path1, const std::string& path2);
    std::string GetFileExtension(const std::string& filename);
    std::string GetFilenameWithoutExtension(const std::string& filename);
    std::string GetDirectoryFromPath(const std::string& path);
    
    bool IsAbsolutePath(const std::string& path);
    std::string NormalizePath(const std::string& path);
    
    // File size and modification time
    size_t GetFileSize(const std::string& filename);
    std::time_t GetFileModificationTime(const std::string& filename);
    
    // Directory operations
    std::vector<std::string> GetDirectoryContents(const std::string& directory);
    bool RemoveDirectory(const std::string& directory);
    bool CopyFile(const std::string& source, const std::string& destination);
    bool MoveFile(const std::string& source, const std::string& destination);
}

// Constants for file management
namespace FileConstants {
    constexpr const char* RESULTS_DIR = "results";
    constexpr const char* OFFSETS_DIR = "offsets";
    constexpr const char* BACKUPS_DIR = "backups";
    constexpr const char* TEMPLATES_DIR = "templates";
    
    constexpr const char* OFFSETS_FILENAME = "offsets.json";
    constexpr const char* HOOKS_FILENAME = "hooks.json";
    constexpr const char* COMBINED_FILENAME = "fivem_data.json";
    constexpr const char* METADATA_FILENAME = "metadata.json";
    
    constexpr const char* CE_TABLE_EXT = ".ct";
    constexpr const char* CPP_HEADER_EXT = ".h";
    constexpr const char* PYTHON_SCRIPT_EXT = ".py";
    constexpr const char* LUA_SCRIPT_EXT = ".lua";
    
    constexpr const char* JSON_EXT = ".json";
    constexpr const char* BACKUP_EXT = ".bak";
    
    constexpr int DEFAULT_MAX_BACKUPS = 10;
    constexpr size_t MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
}
