# 🚀 FiveM Offset Scanner - MODO DIRETO

## ✅ DLL COMPILADA COM SUCESSO!

A DLL foi simplificada para funcionar **DIRETAMENTE** quando injetada, sem complicações!

### 📁 Localização da DLL
```
fivemoffsets\x64\Release\fivemoffsets.dll
```

### 🎯 COMO USAR (MODO DIRETO)

#### Método 1: Cheat Engine (Recomendado)
1. Abra o **Cheat Engine**
2. Selecione o processo do **FiveM** ou **GTA5.exe**
3. Vá em **Memory View** → **Tools** → **Inject DLL**
4. Selecione a DLL: `fivemoffsets.dll`
5. **PRONTO!** A DLL vai executar automaticamente

#### Método 2: Process Hacker
1. Abra o **Process Hacker** como administrador
2. Encontre o processo **FiveM.exe** ou **GTA5.exe**
3. Clique com botão direito → **Miscellaneous** → **Inject DLL**
4. Selecione a DLL: `fivemoffsets.dll`
5. **PRONTO!** A DLL vai executar automaticamente

### 🔥 O QUE ACONTECE QUANDO INJETAR

1. **Console aparece automaticamente** mostrando o progresso
2. **Scan direto** da memória do processo
3. **Criação automática** da pasta `results\offsets\`
4. **Geração de arquivos**:
   - `basic_offsets.json` (dados em JSON)
   - `basic_offsets.h` (headers para C++)
5. **MessageBox** confirmando sucesso

### 📊 EXEMPLO DE SAÍDA

```json
{
  "scan_type": "basic_direct",
  "timestamp": "1703123456",
  "offsets": {
    "ModuleBase": "0x7ff123456000",
    "ModuleSize": "0x12345678",
    "EntryPoint": "0x7ff123457000",
    "ExecutableRegion_0": "0x7ff123456000",
    "ExecutableRegion_4096": "0x7ff123457000"
  }
}
```

### 🎯 VANTAGENS DO MODO DIRETO

- ✅ **Funciona imediatamente** após injeção
- ✅ **Sem verificações complexas** que podem falhar
- ✅ **Console visual** para feedback em tempo real
- ✅ **Scan básico** mas efetivo
- ✅ **Criação automática** de arquivos de resultado
- ✅ **Compatível** com qualquer processo
- ✅ **Não trava** - execução rápida

### 🔧 TROUBLESHOOTING

**Se não aparecer console:**
- Tente injetar como administrador
- Verifique se o antivírus não está bloqueando

**Se não criar arquivos:**
- Verifique permissões da pasta
- Execute o processo como administrador

**Se der erro de injeção:**
- Certifique-se que o processo está rodando
- Tente com Process Hacker em vez de Cheat Engine

### 💡 DICAS

- **Melhor momento**: Injete quando o FiveM estiver no menu principal
- **Processo alvo**: Tanto FiveM.exe quanto GTA5.exe funcionam
- **Arquivos**: Os resultados ficam na pasta onde a DLL foi injetada
- **Logs**: Use DebugView para ver logs detalhados se necessário

### 🎉 SUCESSO!

Quando funcionar corretamente, você verá:
- Console com mensagens de progresso
- MessageBox confirmando sucesso
- Pasta `results\offsets\` criada com arquivos
- Offsets básicos prontos para uso

**A DLL agora funciona de forma DIRETA e SIMPLES!** 🚀
