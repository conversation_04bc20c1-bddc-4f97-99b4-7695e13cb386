#pragma once
#include "framework.h"

class PatternScanner {
public:
    struct <PERSON>tern {
        std::vector<uint8_t> bytes;
        std::vector<bool> mask;
        std::string name;
        std::string description;
        
        Pattern(const std::string& pattern, const std::string& name = "", const std::string& desc = "");
    };

    struct ScanResult {
        uintptr_t address;
        std::string pattern_name;
        std::string description;
        size_t offset_from_base;
        bool is_valid;
        
        ScanResult() : address(0), offset_from_base(0), is_valid(false) {}
        ScanResult(uintptr_t addr, const std::string& name, const std::string& desc, uintptr_t base)
            : address(addr), pattern_name(name), description(desc), 
              offset_from_base(addr - base), is_valid(true) {}
    };

private:
    uintptr_t m_base_address;
    size_t m_module_size;
    std::string m_module_name;
    std::vector<Pattern> m_patterns;
    std::map<std::string, ScanResult> m_results;

public:
    PatternScanner(const std::string& module_name = "");
    ~PatternScanner();

    // Initialization
    bool Initialize(const std::string& module_name = "");
    bool IsInitialized() const { return m_base_address != 0; }

    // Pattern management
    void AddPattern(const std::string& pattern, const std::string& name, const std::string& description = "");
    void AddPattern(const Pattern& pattern);
    void ClearPatterns();

    // Scanning methods
    uintptr_t ScanPattern(const std::string& pattern, size_t start_offset = 0);
    uintptr_t ScanPattern(const Pattern& pattern, size_t start_offset = 0);
    std::vector<uintptr_t> ScanPatternMultiple(const std::string& pattern, size_t max_results = 10);
    
    // Advanced scanning
    bool ScanAllPatterns();
    ScanResult GetResult(const std::string& pattern_name) const;
    std::map<std::string, ScanResult> GetAllResults() const { return m_results; }

    // Utility methods
    uintptr_t GetBaseAddress() const { return m_base_address; }
    size_t GetModuleSize() const { return m_module_size; }
    std::string GetModuleName() const { return m_module_name; }
    
    // Memory reading helpers
    template<typename T>
    T ReadMemory(uintptr_t address) const;
    
    template<typename T>
    bool ReadMemorySafe(uintptr_t address, T& value) const;
    
    std::vector<uint8_t> ReadBytes(uintptr_t address, size_t size) const;
    
    // RVA/Offset calculations
    uintptr_t RVAToAddress(uintptr_t rva) const { return m_base_address + rva; }
    uintptr_t AddressToRVA(uintptr_t address) const { return address - m_base_address; }
    
    // Disassembly helpers
    uintptr_t FollowCall(uintptr_t call_address) const;
    uintptr_t FollowJump(uintptr_t jump_address) const;
    uintptr_t ResolveRelativeAddress(uintptr_t instruction_address, int32_t offset, size_t instruction_size = 5) const;

private:
    // Internal scanning implementation
    bool ComparePattern(const uint8_t* data, const Pattern& pattern) const;
    std::vector<uint8_t> PatternStringToBytes(const std::string& pattern) const;
    std::vector<bool> PatternStringToMask(const std::string& pattern) const;
    
    // Memory protection
    bool IsAddressValid(uintptr_t address) const;
    bool IsAddressReadable(uintptr_t address, size_t size = 1) const;
};

// Template implementations
template<typename T>
T PatternScanner::ReadMemory(uintptr_t address) const {
    if (!IsAddressReadable(address, sizeof(T))) {
        return T{};
    }
    return *reinterpret_cast<T*>(address);
}

template<typename T>
bool PatternScanner::ReadMemorySafe(uintptr_t address, T& value) const {
    if (!IsAddressReadable(address, sizeof(T))) {
        return false;
    }
    
    __try {
        value = *reinterpret_cast<T*>(address);
        return true;
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        return false;
    }
}
