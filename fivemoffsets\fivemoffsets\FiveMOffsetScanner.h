#pragma once
#include "framework.h"
#include "PatternScanner.h"
#include "FiveMStructures.h"
#include "HookManager.h"
#include "FileManager.h"

class FiveMOffsetScanner {
public:
    struct ScanConfig {
        bool enable_pattern_scanning;
        bool enable_structure_detection;
        bool enable_hooking;
        bool enable_auto_export;
        bool enable_logging;
        bool paid_features_enabled;
        std::string log_level;
        
        ScanConfig() : enable_pattern_scanning(true), enable_structure_detection(true),
                      enable_hooking(false), enable_auto_export(true), enable_logging(true),
                      paid_features_enabled(false), log_level("INFO") {}
    };

    struct ScanResults {
        bool success;
        size_t offsets_found;
        size_t hooks_created;
        size_t patterns_matched;
        std::string error_message;
        std::chrono::milliseconds scan_duration;
        
        ScanResults() : success(false), offsets_found(0), hooks_created(0), 
                       patterns_matched(0), scan_duration(0) {}
    };

private:
    std::unique_ptr<PatternScanner> m_pattern_scanner;
    std::unique_ptr<FiveMStructures> m_structures;
    std::unique_ptr<HookManager> m_hook_manager;
    std::unique_ptr<FileManager> m_file_manager;
    
    ScanConfig m_config;
    ScanResults m_last_results;
    bool m_is_initialized;
    std::string m_log_file;

public:
    FiveMOffsetScanner();
    ~FiveMOffsetScanner();

    // Initialization
    bool Initialize(const ScanConfig& config = ScanConfig());
    void Shutdown();
    bool IsInitialized() const { return m_is_initialized; }

    // Main scanning functions
    ScanResults PerformFullScan();
    ScanResults PerformQuickScan();
    ScanResults PerformCustomScan(const std::vector<std::string>& pattern_names);
    
    // Individual component scanning
    bool ScanPatterns();
    bool ScanStructures();
    bool SetupHooks();
    
    // Configuration
    void SetConfig(const ScanConfig& config) { m_config = config; }
    ScanConfig GetConfig() const { return m_config; }
    
    // Results access
    ScanResults GetLastResults() const { return m_last_results; }
    nlohmann::json GetOffsetsJson() const;
    nlohmann::json GetHooksJson() const;
    nlohmann::json GetCombinedJson() const;
    
    // Export functions
    bool ExportResults();
    bool ExportToFile(const std::string& filename);
    bool ExportCheatEngineTable(const std::string& filename);
    bool ExportCppHeader(const std::string& filename);
    
    // Utility functions
    std::vector<std::string> GetAvailablePatterns() const;
    std::vector<std::string> GetFoundOffsets() const;
    std::vector<std::string> GetActiveHooks() const;
    
    // Logging
    void SetLogLevel(const std::string& level);
    void EnableLogging(bool enable);
    std::string GetLogFile() const { return m_log_file; }
    
    // Status information
    std::string GetStatusString() const;
    nlohmann::json GetStatusJson() const;
    
    // Advanced features (paid users only)
    bool EnablePaidFeatures(const std::string& license_key);
    bool IsFeatureAvailable(const std::string& feature_name) const;
    
    // Real-time monitoring
    void StartRealTimeMonitoring();
    void StopRealTimeMonitoring();
    bool IsMonitoring() const;

private:
    void InitializeLogging();
    void LogMessage(const std::string& level, const std::string& message);
    void LogError(const std::string& message);
    void LogInfo(const std::string& message);
    void LogDebug(const std::string& message);
    
    bool ValidateLicense(const std::string& license_key);
    void UpdateScanResults(const ScanResults& results);
    
    // Internal scanning helpers
    std::chrono::milliseconds MeasureScanTime(std::function<bool()> scan_function);
    bool PerformScanWithTiming(const std::string& scan_name, std::function<bool()> scan_function);
};

// Global instance for easy access
extern std::unique_ptr<FiveMOffsetScanner> g_OffsetScanner;

// DLL export functions for external access
extern "C" {
    __declspec(dllexport) bool InitializeScanner();
    __declspec(dllexport) bool PerformScan();
    __declspec(dllexport) bool ExportResults();
    __declspec(dllexport) const char* GetResultsJson();
    __declspec(dllexport) const char* GetStatusString();
    __declspec(dllexport) void ShutdownScanner();
    
    // Paid features
    __declspec(dllexport) bool EnableHooking(const char* license_key);
    __declspec(dllexport) bool SetupHook(const char* hook_name);
    __declspec(dllexport) bool RemoveHook(const char* hook_name);
}

// Utility macros
#define SCAN_LOG_INFO(msg) if (m_config.enable_logging) LogInfo(msg)
#define SCAN_LOG_ERROR(msg) if (m_config.enable_logging) LogError(msg)
#define SCAN_LOG_DEBUG(msg) if (m_config.enable_logging && m_config.log_level == "DEBUG") LogDebug(msg)

// Feature availability constants
namespace Features {
    constexpr const char* PATTERN_SCANNING = "pattern_scanning";
    constexpr const char* STRUCTURE_DETECTION = "structure_detection";
    constexpr const char* BASIC_EXPORT = "basic_export";
    constexpr const char* HOOKING = "hooking";
    constexpr const char* ADVANCED_PATTERNS = "advanced_patterns";
    constexpr const char* REAL_TIME_MONITORING = "real_time_monitoring";
    constexpr const char* CHEAT_ENGINE_EXPORT = "cheat_engine_export";
    constexpr const char* CODE_GENERATION = "code_generation";
}

// Error codes
namespace ErrorCodes {
    constexpr int SUCCESS = 0;
    constexpr int INITIALIZATION_FAILED = 1;
    constexpr int PATTERN_SCAN_FAILED = 2;
    constexpr int STRUCTURE_SCAN_FAILED = 3;
    constexpr int HOOK_SETUP_FAILED = 4;
    constexpr int EXPORT_FAILED = 5;
    constexpr int LICENSE_INVALID = 6;
    constexpr int FEATURE_NOT_AVAILABLE = 7;
    constexpr int FILE_ACCESS_ERROR = 8;
    constexpr int MEMORY_ACCESS_ERROR = 9;
    constexpr int UNKNOWN_ERROR = 99;
}
